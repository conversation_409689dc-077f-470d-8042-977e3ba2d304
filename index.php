<?php
/**
 * Professional Arabic E-commerce Homepage - Rebuilt from Scratch
 * Modern, Clean, and Error-Free Implementation
 * Complete homepage with all required sections in proper order
 *
 * @version 5.0
 * <AUTHOR> Development Team
 * @description Complete homepage rebuild with white background, black/gray text,
 *              rectangular buttons, and professional Arabic RTL design
 */

// Set page title and meta information
$pageTitle = 'الرئيسية - متجر Care للعناية والجمال';
$pageDescription = 'اكتشفي سر الجمال الطبيعي مع Care - متجر العناية والجمال الأول في العراق. منتجات أصلية، توصيل مجاني، وأفضل الأسعار.';
$pageKeywords = 'متجر Care, عناية بالشعر, عناية بالبشرة, مكياج, عطور, منتجات جمال, العراق';

// Include configuration and header
require_once 'config/config.php';
require_once 'includes/header.php';

// Include new CSS file
echo '<link href="assets/css/homepage-new.css" rel="stylesheet">';

// Initialize database connection and check tables
try {
    // Ensure required tables exist
    $requiredTables = [
        'products' => 'Products table',
        'categories' => 'Categories table', 
        'reviews' => 'Reviews table'
    ];
    
    foreach ($requiredTables as $table => $description) {
        $tableExists = fetchOne("SHOW TABLES LIKE '$table'");
        if (!$tableExists) {
            error_log("Warning: $description ($table) does not exist");
        }
    }
} catch (Exception $e) {
    error_log("Database initialization error: " . $e->getMessage());
}

// Fetch data for homepage sections
try {
    // Fetch categories for categories section
    $categories = fetchAll("
        SELECT id, name, description, image, status 
        FROM categories 
        WHERE status = 'active' 
        ORDER BY name ASC 
        LIMIT 8
    ") ?: [];

    // Fetch featured products
    $featuredProducts = fetchAll("
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active' AND p.is_featured = 1
        ORDER BY p.created_at DESC
        LIMIT 12
    ") ?: [];

    // Fetch special offers (products with discounts)
    $specialOffers = fetchAll("
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active' AND p.discount > 0
        ORDER BY p.discount DESC, p.created_at DESC
        LIMIT 12
    ") ?: [];

    // Fetch customer reviews
    $customerReviews = fetchAll("
        SELECT r.*, p.name as product_name
        FROM reviews r
        LEFT JOIN products p ON r.product_id = p.id
        WHERE r.status = 'approved'
        ORDER BY r.created_at DESC
        LIMIT 6
    ") ?: [];

    // Fetch influencers content
    $influencers = [];
    if (function_exists('tableExists') && tableExists('influencers_content')) {
        $influencers = fetchAll("
            SELECT influencer_name, influencer_image, content_title, content_text, rating, is_featured, sort_order, published_at, created_at
            FROM influencers_content
            WHERE status = 'published'
            ORDER BY is_featured DESC, sort_order ASC, published_at DESC, created_at DESC
            LIMIT 6
        ") ?: [];
    }

} catch (Exception $e) {
    error_log("Error fetching homepage data: " . $e->getMessage());
    // Initialize empty arrays as fallback
    $categories = [];
    $featuredProducts = [];
    $specialOffers = [];
    $customerReviews = [];
    $influencers = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($pageKeywords); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($pageTitle); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($pageTitle); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="twitter:image" content="<?php echo SITE_URL; ?>/assets/images/twitter-card.jpg">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo SITE_URL; ?>">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Store",
        "name": "متجر Care للعناية والجمال",
        "description": "<?php echo htmlspecialchars($pageDescription); ?>",
        "url": "<?php echo SITE_URL; ?>",
        "logo": "<?php echo SITE_URL; ?>/assets/images/logo.png",
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "IQ",
            "addressLocality": "بغداد"
        },
        "currenciesAccepted": "IQD",
        "paymentAccepted": "Cash"
    }
    </script>
</head>
<body>

<!-- ========================================================================
     PROFESSIONAL HOMEPAGE CONTENT - REBUILT FROM SCRATCH
     Modern Arabic RTL E-commerce Design with Error-Free Implementation
     ======================================================================== -->

<main id="main-content" role="main">

<!-- 1. Hero Carousel Section - 5 Slides, 5-second auto-advance -->
<?php include 'includes/homepage_carousel.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 2. Categories Section - Attractive category cards with icons -->
<?php include 'includes/homepage_categories.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 3. Featured Products Section - 6 columns desktop, Swiper mobile -->
<?php include 'includes/homepage_featured_products.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 4. Special Offers Section - Discounted prices, crossed out original prices -->
<?php include 'includes/homepage_special_offers.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 5. About Store Section - Professional introduction to Care store -->
<?php include 'includes/homepage_about_store.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 6. Customer Reviews Section - Professional testimonials with database integration -->
<?php include 'includes/homepage_customer_reviews.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 7. Influencers Section - Professional brand ambassadors with social media integration -->
<?php include 'includes/homepage_influencers.php'; ?>

<!-- 8. Why Choose Us Section - Professional features and advantages with statistics -->
<?php include 'includes/homepage_why_choose_us.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 9. Newsletter Section - Professional subscription with AJAX and benefits -->
<?php include 'includes/homepage_newsletter.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 10. Social Media Section - Professional social media integration with feed preview -->
<?php include 'includes/homepage_social_media.php'; ?>

<!-- Back to Top Button -->
<button id="backToTop" class="back-to-top" title="العودة للأعلى">
    <i class="bi bi-arrow-up"></i>
</button>

<!-- Toast Notification Container -->
<div id="toastContainer" class="toast-container"></div>

</main>

<?php
// Include footer
require_once 'includes/footer.php';
?>
