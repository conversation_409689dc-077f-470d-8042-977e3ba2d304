<?php
/**
 * Professional Arabic E-commerce Homepage - Rebuilt from Scratch
 * Modern, Clean, and Error-Free Implementation
 * Complete homepage with all required sections in proper order
 *
 * @version 5.0
 * <AUTHOR> Development Team
 * @description Complete homepage rebuild with white background, black/gray text,
 *              rectangular buttons, and professional Arabic RTL design
 */

// Set page title and meta information
$pageTitle = 'الرئيسية - متجر Care للعناية والجمال';
$pageDescription = 'اكتشفي سر الجمال الطبيعي مع Care - متجر العناية والجمال الأول في العراق. منتجات أصلية، توصيل مجاني، وأفضل الأسعار.';
$pageKeywords = 'متجر Care, عناية بالشعر, عناية بالبشرة, مكياج, عطور, منتجات جمال, العراق';

// Include configuration and header
require_once 'config/config.php';
require_once 'includes/header.php';

// Include new CSS file
echo '<link href="assets/css/homepage-new.css" rel="stylesheet">';

// Initialize database connection and check tables
try {
    // Ensure required tables exist
    $requiredTables = [
        'products' => 'Products table',
        'categories' => 'Categories table', 
        'reviews' => 'Reviews table'
    ];
    
    foreach ($requiredTables as $table => $description) {
        $tableExists = fetchOne("SHOW TABLES LIKE '$table'");
        if (!$tableExists) {
            error_log("Warning: $description ($table) does not exist");
        }
    }
} catch (Exception $e) {
    error_log("Database initialization error: " . $e->getMessage());
}

// Fetch data for homepage sections
try {
    // Fetch categories for categories section
    $categories = fetchAll("
        SELECT id, name, description, image, status 
        FROM categories 
        WHERE status = 'active' 
        ORDER BY name ASC 
        LIMIT 8
    ") ?: [];

    // Fetch featured products
    $featuredProducts = fetchAll("
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active' AND p.is_featured = 1
        ORDER BY p.created_at DESC
        LIMIT 12
    ") ?: [];

    // Fetch special offers (products with discounts)
    $specialOffers = fetchAll("
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active' AND p.discount > 0
        ORDER BY p.discount DESC, p.created_at DESC
        LIMIT 12
    ") ?: [];

    // Fetch customer reviews
    $customerReviews = fetchAll("
        SELECT r.*, p.name as product_name
        FROM reviews r
        LEFT JOIN products p ON r.product_id = p.id
        WHERE r.status = 'approved'
        ORDER BY r.created_at DESC
        LIMIT 6
    ") ?: [];

    // Fetch influencers content
    $influencers = [];
    if (function_exists('tableExists') && tableExists('influencers_content')) {
        $influencers = fetchAll("
            SELECT influencer_name, influencer_image, content_title, content_text, rating, is_featured, sort_order, published_at, created_at
            FROM influencers_content
            WHERE status = 'published'
            ORDER BY is_featured DESC, sort_order ASC, published_at DESC, created_at DESC
            LIMIT 6
        ") ?: [];
    }

} catch (Exception $e) {
    error_log("Error fetching homepage data: " . $e->getMessage());
    // Initialize empty arrays as fallback
    $categories = [];
    $featuredProducts = [];
    $specialOffers = [];
    $customerReviews = [];
    $influencers = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($pageKeywords); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($pageTitle); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($pageTitle); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="twitter:image" content="<?php echo SITE_URL; ?>/assets/images/twitter-card.jpg">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo SITE_URL; ?>">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Store",
        "name": "متجر Care للعناية والجمال",
        "description": "<?php echo htmlspecialchars($pageDescription); ?>",
        "url": "<?php echo SITE_URL; ?>",
        "logo": "<?php echo SITE_URL; ?>/assets/images/logo.png",
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "IQ",
            "addressLocality": "بغداد"
        },
        "currenciesAccepted": "IQD",
        "paymentAccepted": "Cash"
    }
    </script>
</head>
<body>

<!-- ========================================================================
     PROFESSIONAL HOMEPAGE CONTENT - REBUILT FROM SCRATCH
     Modern Arabic RTL E-commerce Design with Error-Free Implementation
     ======================================================================== -->

<!-- 1. Hero Carousel Section - 5 Slides, 5-second auto-advance -->
<?php include 'includes/homepage_carousel.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 2. Categories Section - Attractive category cards with icons -->
<?php include 'includes/homepage_categories.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 3. Featured Products Section - 6 columns desktop, Swiper mobile -->
<?php include 'includes/homepage_featured_products.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 4. Special Offers Section - Discounted prices, crossed out original prices -->
<?php include 'includes/homepage_special_offers.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 5. About Store Section - Professional introduction to Care store -->
<?php include 'includes/homepage_about_store.php'; ?>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 6. Customer Reviews Section - Testimonials carousel/grid -->
<section class="section reviews-section" id="customer-reviews">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">آراء العملاء</h2>
            <p class="section-subtitle">اكتشف ما يقوله عملاؤنا الكرام عن تجربتهم معنا</p>
        </div>

        <?php if (!empty($customerReviews)): ?>
        <div class="reviews-grid grid grid-cols-3">
            <?php foreach (array_slice($customerReviews, 0, 6) as $index => $review): ?>
            <div class="review-card fade-in-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                <div class="review-rating">
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                        <i class="star <?php echo $i <= $review['rating'] ? '' : 'empty'; ?>">★</i>
                    <?php endfor; ?>
                </div>
                <p class="review-text">"<?php echo htmlspecialchars($review['comment']); ?>"</p>
                <div class="review-author">
                    <div class="author-name"><?php echo htmlspecialchars($review['customer_name']); ?></div>
                    <?php if (!empty($review['product_name'])): ?>
                    <div class="author-title">عميل منتج: <?php echo htmlspecialchars($review['product_name']); ?></div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php else: ?>
        <div class="no-reviews text-center">
            <div class="no-reviews-icon">
                <i class="bi bi-chat-quote" aria-hidden="true"></i>
            </div>
            <h3 class="no-reviews-title">لا توجد تقييمات حالياً</h3>
            <p class="no-reviews-text">كن أول من يشارك تجربته معنا</p>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 7. Influencers Section - Brand ambassadors -->
<?php if (!empty($influencers)): ?>
<section class="section influencers-section" id="influencers">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">سفراء العلامة التجارية</h2>
            <p class="section-subtitle">تعرف على المؤثرين الذين يثقون بمنتجاتنا</p>
        </div>

        <div class="influencers-grid grid grid-cols-3">
            <?php foreach (array_slice($influencers, 0, 6) as $index => $influencer): ?>
            <div class="influencer-card fade-in-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                <?php if (!empty($influencer['influencer_image'])): ?>
                <div class="influencer-image-container">
                    <img src="<?php echo htmlspecialchars($influencer['influencer_image']); ?>"
                         alt="<?php echo htmlspecialchars($influencer['influencer_name']); ?>"
                         class="influencer-image"
                         loading="lazy">
                </div>
                <?php endif; ?>
                
                <div class="influencer-content">
                    <h3 class="influencer-name"><?php echo htmlspecialchars($influencer['influencer_name']); ?></h3>
                    
                    <?php if (!empty($influencer['content_title'])): ?>
                    <div class="influencer-title"><?php echo htmlspecialchars($influencer['content_title']); ?></div>
                    <?php endif; ?>
                    
                    <p class="influencer-description"><?php echo htmlspecialchars($influencer['content_text']); ?></p>
                    
                    <?php if (!empty($influencer['rating'])): ?>
                    <div class="influencer-rating">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="star <?php echo $i <= $influencer['rating'] ? '' : 'empty'; ?>">★</i>
                        <?php endfor; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Section Separator -->
<div class="section-separator"></div>
<?php endif; ?>

<!-- 8. Why Choose Us Section - Store advantages and services -->
<section class="section features-section" id="why-choose-us">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">لماذا اختارنا؟</h2>
            <p class="section-subtitle">نقدم لك أفضل تجربة تسوق مع خدمات متميزة</p>
        </div>

        <div class="features-grid grid grid-cols-4">
            <div class="feature-card fade-in-up">
                <i class="feature-icon bi bi-shield-check"></i>
                <h3 class="feature-title">منتجات أصلية 100%</h3>
                <p class="feature-description">جميع منتجاتنا أصلية ومضمونة من المصدر مع شهادات الجودة</p>
            </div>

            <div class="feature-card fade-in-up" style="animation-delay: 0.1s;">
                <i class="feature-icon bi bi-truck"></i>
                <h3 class="feature-title">توصيل سريع ومجاني</h3>
                <p class="feature-description">توصيل مجاني لجميع أنحاء العراق خلال 24-48 ساعة</p>
            </div>

            <div class="feature-card fade-in-up" style="animation-delay: 0.2s;">
                <i class="feature-icon bi bi-headset"></i>
                <h3 class="feature-title">خدمة عملاء متميزة</h3>
                <p class="feature-description">فريق دعم متخصص متاح 24/7 لخدمتكم والإجابة على استفساراتكم</p>
            </div>

            <div class="feature-card fade-in-up" style="animation-delay: 0.3s;">
                <i class="feature-icon bi bi-arrow-clockwise"></i>
                <h3 class="feature-title">ضمان الإرجاع</h3>
                <p class="feature-description">إمكانية الإرجاع والاستبدال خلال 14 يوم مع ضمان استرداد المبلغ</p>
            </div>
        </div>
    </div>
</section>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 9. Newsletter Section - Email subscription -->
<section class="newsletter-section" id="newsletter">
    <div class="container">
        <h2 class="newsletter-title">اشترك في النشرة الإخبارية</h2>
        <p class="newsletter-subtitle">احصل على آخر العروض والمنتجات الجديدة مباشرة في بريدك الإلكتروني</p>

        <form class="newsletter-form" id="newsletterForm">
            <input type="email"
                   class="newsletter-input"
                   placeholder="أدخل بريدك الإلكتروني"
                   required>
            <button type="submit" class="btn-newsletter">
                <i class="bi bi-envelope"></i>
                <span>اشتراك</span>
            </button>
        </form>

        <div class="newsletter-benefits">
            <div class="benefit-item">
                <i class="bi bi-percent"></i>
                <span>عروض حصرية</span>
            </div>
            <div class="benefit-item">
                <i class="bi bi-bell"></i>
                <span>إشعارات المنتجات الجديدة</span>
            </div>
            <div class="benefit-item">
                <i class="bi bi-gift"></i>
                <span>هدايا مجانية</span>
            </div>
        </div>
    </div>
</section>

<!-- Section Separator -->
<div class="section-separator"></div>

<!-- 10. Social Media Section - Social media links and icons -->
<section class="social-section" id="social-media">
    <div class="container">
        <h2 class="social-title">تابعنا على مواقع التواصل الاجتماعي</h2>

        <div class="social-links">
            <a href="#" class="social-link facebook" title="فيسبوك">
                <i class="bi bi-facebook"></i>
            </a>
            <a href="#" class="social-link instagram" title="إنستغرام">
                <i class="bi bi-instagram"></i>
            </a>
            <a href="#" class="social-link twitter" title="تويتر">
                <i class="bi bi-twitter"></i>
            </a>
            <a href="#" class="social-link youtube" title="يوتيوب">
                <i class="bi bi-youtube"></i>
            </a>
            <a href="#" class="social-link whatsapp" title="واتساب">
                <i class="bi bi-whatsapp"></i>
            </a>
        </div>

        <p class="social-description">
            انضم إلى مجتمعنا المتنامي واحصل على نصائح الجمال والعناية اليومية
        </p>
    </div>
</section>

<!-- Back to Top Button -->
<button id="backToTop" class="back-to-top" title="العودة للأعلى">
    <i class="bi bi-arrow-up"></i>
</button>

<!-- Toast Notification Container -->
<div id="toastContainer" class="toast-container"></div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
