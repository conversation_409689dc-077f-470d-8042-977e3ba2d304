<?php
/**
 * Professional Special Offers Section Component
 * Modern Arabic E-commerce Special Offers Display
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description Special offers with discounted prices, original prices crossed out, discount badges
 */

// Fetch products with discounts from database
$specialOffers = [];
try {
    $offersQuery = "
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active' AND p.discount > 0
        ORDER BY p.discount DESC, p.created_at DESC
        LIMIT 12
    ";
    $specialOffers = fetchAll($offersQuery);
    
    if (!$specialOffers) {
        $specialOffers = [];
    }
} catch (Exception $e) {
    error_log("Error fetching special offers: " . $e->getMessage());
    $specialOffers = [];
}

// Function to format Iraqi Dinar price
function formatIraqiPrice($price) {
    return number_format($price, 0, '.', ',');
}

// Function to calculate discounted price
function getDiscountedPrice($price, $discount) {
    if ($discount > 0) {
        return $price - ($price * $discount / 100);
    }
    return $price;
}

// Function to calculate savings amount
function getSavingsAmount($price, $discount) {
    if ($discount > 0) {
        return $price * $discount / 100;
    }
    return 0;
}
?>

<!-- Special Offers Section - Professional Design -->
<section class="section special-offers-section" id="special-offers">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header">
            <h2 class="section-title">العروض الخاصة</h2>
            <p class="section-subtitle">اغتنم الفرصة واحصل على أفضل العروض والخصومات الحصرية</p>
        </div>

        <?php if (!empty($specialOffers)): ?>
        <!-- Desktop Grid View -->
        <div class="special-offers-desktop d-none d-lg-block">
            <div class="offers-grid grid grid-cols-6">
                <?php foreach (array_slice($specialOffers, 0, 6) as $index => $product): ?>
                <div class="offer-card fade-in-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                    <!-- Discount Badge - Mandatory -->
                    <div class="discount-badge">
                        <span class="discount-percentage">تخفيض <?php echo $product['discount']; ?>%</span>
                        <span class="savings-amount">وفر <?php echo formatIraqiPrice(getSavingsAmount($product['price'], $product['discount'])); ?> د.ع</span>
                    </div>

                    <!-- Product Image -->
                    <div class="product-image-container">
                        <img src="<?php echo htmlspecialchars(getProductImage($product)); ?>"
                             class="product-image"
                             alt="<?php echo htmlspecialchars($product['name']); ?>"
                             loading="<?php echo $index < 3 ? 'eager' : 'lazy'; ?>">
                        
                        <!-- Quick Actions Overlay -->
                        <div class="product-overlay">
                            <button class="btn-quick-action btn-quick-view" 
                                    data-product-id="<?php echo $product['id']; ?>"
                                    title="مشاهدة سريعة">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn-quick-action btn-add-wishlist" 
                                    data-product-id="<?php echo $product['id']; ?>"
                                    title="إضافة للمفضلة">
                                <i class="bi bi-heart"></i>
                            </button>
                        </div>

                        <!-- Limited Time Indicator -->
                        <div class="limited-time-indicator">
                            <i class="bi bi-clock"></i>
                            <span>عرض محدود</span>
                        </div>
                    </div>

                    <!-- Product Content -->
                    <div class="product-content">
                        <h3 class="product-title">
                            <a href="product.php?id=<?php echo $product['id']; ?>">
                                <?php echo htmlspecialchars($product['name']); ?>
                            </a>
                        </h3>

                        <?php if (!empty($product['short_description'])): ?>
                        <p class="product-description">
                            <?php echo htmlspecialchars($product['short_description']); ?>
                        </p>
                        <?php endif; ?>

                        <!-- Product Price - Special Offers Format -->
                        <div class="product-price offer-price">
                            <div class="price-row">
                                <span class="price-current offer-price-current">
                                    <?php echo formatIraqiPrice(getDiscountedPrice($product['price'], $product['discount'])); ?>
                                    <span class="currency">د.ع</span>
                                </span>
                                <span class="price-original offer-price-original">
                                    <?php echo formatIraqiPrice($product['price']); ?>
                                    <span class="currency">د.ع</span>
                                </span>
                            </div>
                            <div class="savings-info">
                                <span class="savings-text">
                                    توفر <?php echo formatIraqiPrice(getSavingsAmount($product['price'], $product['discount'])); ?> د.ع
                                </span>
                            </div>
                        </div>

                        <!-- Product Actions -->
                        <div class="product-actions">
                            <button class="btn-add-cart offer-btn-cart" 
                                    data-product-id="<?php echo $product['id']; ?>"
                                    data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                                    data-product-price="<?php echo getDiscountedPrice($product['price'], $product['discount']); ?>">
                                <i class="bi bi-bag-plus"></i>
                                <span>اشتري الآن</span>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Mobile Swiper View -->
        <div class="special-offers-mobile d-lg-none">
            <div class="swiper special-offers-swiper">
                <div class="swiper-wrapper">
                    <?php foreach ($specialOffers as $index => $product): ?>
                    <div class="swiper-slide">
                        <div class="offer-card">
                            <!-- Discount Badge - Mandatory -->
                            <div class="discount-badge">
                                <span class="discount-percentage">تخفيض <?php echo $product['discount']; ?>%</span>
                                <span class="savings-amount">وفر <?php echo formatIraqiPrice(getSavingsAmount($product['price'], $product['discount'])); ?> د.ع</span>
                            </div>

                            <!-- Product Image -->
                            <div class="product-image-container">
                                <img src="<?php echo htmlspecialchars(getProductImage($product)); ?>"
                                     class="product-image"
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     loading="lazy">
                                
                                <!-- Quick Actions Overlay -->
                                <div class="product-overlay">
                                    <button class="btn-quick-action btn-quick-view" 
                                            data-product-id="<?php echo $product['id']; ?>"
                                            title="مشاهدة سريعة">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button class="btn-quick-action btn-add-wishlist" 
                                            data-product-id="<?php echo $product['id']; ?>"
                                            title="إضافة للمفضلة">
                                        <i class="bi bi-heart"></i>
                                    </button>
                                </div>

                                <!-- Limited Time Indicator -->
                                <div class="limited-time-indicator">
                                    <i class="bi bi-clock"></i>
                                    <span>عرض محدود</span>
                                </div>
                            </div>

                            <!-- Product Content -->
                            <div class="product-content">
                                <h3 class="product-title">
                                    <a href="product.php?id=<?php echo $product['id']; ?>">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                    </a>
                                </h3>

                                <?php if (!empty($product['short_description'])): ?>
                                <p class="product-description">
                                    <?php echo htmlspecialchars($product['short_description']); ?>
                                </p>
                                <?php endif; ?>

                                <!-- Product Price - Special Offers Format -->
                                <div class="product-price offer-price">
                                    <div class="price-row">
                                        <span class="price-current offer-price-current">
                                            <?php echo formatIraqiPrice(getDiscountedPrice($product['price'], $product['discount'])); ?>
                                            <span class="currency">د.ع</span>
                                        </span>
                                        <span class="price-original offer-price-original">
                                            <?php echo formatIraqiPrice($product['price']); ?>
                                            <span class="currency">د.ع</span>
                                        </span>
                                    </div>
                                    <div class="savings-info">
                                        <span class="savings-text">
                                            توفر <?php echo formatIraqiPrice(getSavingsAmount($product['price'], $product['discount'])); ?> د.ع
                                        </span>
                                    </div>
                                </div>

                                <!-- Product Actions -->
                                <div class="product-actions">
                                    <button class="btn-add-cart offer-btn-cart" 
                                            data-product-id="<?php echo $product['id']; ?>"
                                            data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                                            data-product-price="<?php echo getDiscountedPrice($product['price'], $product['discount']); ?>">
                                        <i class="bi bi-bag-plus"></i>
                                        <span>اشتري الآن</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Swiper Navigation -->
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
                
                <!-- Swiper Pagination -->
                <div class="swiper-pagination"></div>
            </div>
        </div>

        <!-- View All Offers Button -->
        <div class="section-footer text-center mt-5">
            <a href="offers.php" class="btn btn-secondary btn-lg">
                <i class="bi bi-percent" aria-hidden="true"></i>
                <span>عرض جميع العروض</span>
            </a>
        </div>

        <?php else: ?>
        <!-- No Offers Available -->
        <div class="no-offers text-center">
            <div class="no-offers-icon">
                <i class="bi bi-percent" aria-hidden="true"></i>
            </div>
            <h3 class="no-offers-title">لا توجد عروض خاصة حالياً</h3>
            <p class="no-offers-text">سيتم إضافة العروض الخاصة قريباً. تابعونا للحصول على آخر العروض والخصومات.</p>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Special Offers Section Styles -->
<style>
/* Special Offers Section Specific Styles */
.special-offers-section {
    background: linear-gradient(135deg, #fef7f0 0%, #fff5f5 100%);
    padding: var(--spacing-4xl) 0;
    position: relative;
}

.special-offers-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ff6b6b" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffa726" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.offers-grid {
    gap: var(--spacing-lg);
}

/* Offer Card Styles - Enhanced for Special Offers */
.offer-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    overflow: hidden;
    position: relative;
    border: 2px solid transparent;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.offer-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-8px);
    border-color: var(--accent-color);
}

.offer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color) 0%, var(--warning-color) 100%);
    z-index: 1;
}

/* Discount Badge - Prominent Design */
.discount-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: linear-gradient(135deg, var(--accent-color) 0%, #ff4757 100%);
    color: var(--text-light);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    z-index: 4;
    text-align: center;
    box-shadow: var(--shadow-lg);
    transform: rotate(-5deg);
    animation: pulse 2s infinite;
}

.discount-percentage {
    display: block;
    font-size: var(--text-sm);
    font-weight: var(--font-bold);
    line-height: 1.2;
}

.savings-amount {
    display: block;
    font-size: var(--text-xs);
    opacity: 0.9;
    margin-top: var(--spacing-xs);
}

@keyframes pulse {
    0%, 100% { transform: rotate(-5deg) scale(1); }
    50% { transform: rotate(-5deg) scale(1.05); }
}

/* Limited Time Indicator */
.limited-time-indicator {
    position: absolute;
    bottom: var(--spacing-md);
    left: var(--spacing-md);
    background-color: rgba(255, 255, 255, 0.95);
    color: var(--warning-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    z-index: 3;
    backdrop-filter: blur(10px);
}

/* Offer Price Styles - Enhanced */
.offer-price {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-md);
}

.price-row {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.offer-price-current {
    font-size: var(--text-2xl);
    color: var(--accent-color);
    font-weight: var(--font-bold);
}

.offer-price-original {
    font-size: var(--text-lg);
    color: var(--text-muted);
    text-decoration: line-through;
    position: relative;
}

.offer-price-original::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--accent-color);
    transform: translateY(-50%);
}

.savings-info {
    text-align: center;
}

.savings-text {
    background-color: var(--success-color);
    color: var(--text-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    display: inline-block;
}

/* Offer Button - Enhanced */
.offer-btn-cart {
    background: linear-gradient(135deg, var(--accent-color) 0%, #ff4757 100%);
    color: var(--text-light);
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    width: 100%;
    position: relative;
    overflow: hidden;
}

.offer-btn-cart::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.offer-btn-cart:hover::before {
    left: 100%;
}

.offer-btn-cart:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Swiper Styles for Offers */
.special-offers-swiper {
    padding: var(--spacing-lg) 0 var(--spacing-2xl);
}

.special-offers-swiper .swiper-slide {
    height: auto;
}

.special-offers-swiper .swiper-button-next,
.special-offers-swiper .swiper-button-prev {
    color: var(--accent-color);
    background-color: rgba(255, 255, 255, 0.9);
    width: 44px;
    height: 44px;
    border-radius: 50%;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.special-offers-swiper .swiper-button-next:hover,
.special-offers-swiper .swiper-button-prev:hover {
    background-color: var(--accent-color);
    color: var(--text-light);
    transform: scale(1.1);
}

.special-offers-swiper .swiper-pagination-bullet {
    background-color: var(--text-muted);
    opacity: 0.5;
}

.special-offers-swiper .swiper-pagination-bullet-active {
    background-color: var(--accent-color);
    opacity: 1;
}

/* No Offers State */
.no-offers {
    padding: var(--spacing-4xl) 0;
}

.no-offers-icon {
    font-size: var(--text-6xl);
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

.no-offers-title {
    font-size: var(--text-2xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.no-offers-text {
    font-size: var(--text-base);
    color: var(--text-secondary);
    max-width: 500px;
    margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .offers-grid.grid-cols-6 {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 992px) {
    .offers-grid.grid-cols-6 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .special-offers-section {
        padding: var(--spacing-3xl) 0;
    }

    .discount-badge {
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .discount-percentage {
        font-size: var(--text-xs);
    }

    .savings-amount {
        font-size: 10px;
    }

    .offer-price-current {
        font-size: var(--text-xl);
    }

    .offer-price-original {
        font-size: var(--text-base);
    }
}
</style>

<!-- Special Offers JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Swiper for mobile offers
    const offersSwiper = new Swiper('.special-offers-swiper', {
        slidesPerView: 1,
        spaceBetween: 20,
        loop: true,
        autoplay: {
            delay: 3500,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        breakpoints: {
            480: {
                slidesPerView: 2,
                spaceBetween: 20,
            },
            768: {
                slidesPerView: 2,
                spaceBetween: 24,
            }
        }
    });

    // Enhanced add to cart for offers
    document.querySelectorAll('.offer-btn-cart').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            const productName = this.dataset.productName;
            const productPrice = this.dataset.productPrice;

            // Add loading state with special animation
            const originalContent = this.innerHTML;
            this.innerHTML = '<i class="bi bi-arrow-repeat"></i> <span>جاري الإضافة...</span>';
            this.disabled = true;
            this.style.background = 'linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%)';

            // Simulate add to cart (replace with actual AJAX call)
            setTimeout(() => {
                this.innerHTML = '<i class="bi bi-check-circle"></i> <span>تمت الإضافة بنجاح!</span>';
                this.style.background = 'linear-gradient(135deg, var(--success-color) 0%, #16a34a 100%)';

                // Show success message with special offer context
                showToast(`تم إضافة "${productName}" إلى السلة بسعر مخفض!`, 'success');

                // Add celebration effect
                createCelebrationEffect(this);

                // Reset button after 3 seconds
                setTimeout(() => {
                    this.innerHTML = originalContent;
                    this.style.background = '';
                    this.disabled = false;
                }, 3000);
            }, 1200);
        });
    });

    // Celebration effect for successful add to cart
    function createCelebrationEffect(button) {
        const rect = button.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        for (let i = 0; i < 6; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 8px;
                height: 8px;
                background: var(--warning-color);
                border-radius: 50%;
                pointer-events: none;
                z-index: 9999;
                left: ${centerX}px;
                top: ${centerY}px;
                animation: celebrate 1s ease-out forwards;
                animation-delay: ${i * 0.1}s;
            `;

            document.body.appendChild(particle);

            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 1000);
        }
    }

    // Add celebration animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes celebrate {
            0% {
                transform: translate(0, 0) scale(1);
                opacity: 1;
            }
            100% {
                transform: translate(${Math.random() * 200 - 100}px, ${Math.random() * 200 - 100}px) scale(0);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});
</script>
