<?php
/**
 * Professional About Store Section Component
 * Modern Arabic E-commerce About Store Display
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description Professional introduction section about Care store with brand information
 */
?>

<!-- About Store Section - Professional Design -->
<section class="section about-store-section" id="about-store">
    <div class="container">
        <!-- Section Content -->
        <div class="about-content">
            <!-- Text Content -->
            <div class="about-text">
                <div class="about-header">
                    <span class="about-label">من نحن</span>
                    <h2 class="about-title">متجر Care للعناية والجمال</h2>
                </div>

                <div class="about-description">
                    <p class="lead-text">
                        نحن في متجر Care نؤمن بأن الجمال الحقيقي يبدأ من العناية الصحيحة. منذ تأسيسنا، 
                        نسعى لتقديم أفضل منتجات العناية بالشعر والبشرة من أرقى العلامات التجارية العالمية.
                    </p>

                    <p>
                        نختار منتجاتنا بعناية فائقة لضمان الجودة والفعالية، ونقدم لعملائنا الكرام 
                        تجربة تسوق استثنائية مع خدمة عملاء متميزة وتوصيل سريع وآمن لجميع أنحاء العراق.
                    </p>

                    <p>
                        رؤيتنا هي أن نكون الوجهة الأولى لكل من يبحث عن منتجات العناية الأصلية والعالية الجودة، 
                        مع تقديم أسعار تنافسية وعروض حصرية لعملائنا المميزين.
                    </p>
                </div>

                <!-- Store Features -->
                <div class="store-features">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <div class="feature-content">
                            <h4 class="feature-title">منتجات أصلية 100%</h4>
                            <p class="feature-description">جميع منتجاتنا أصلية ومضمونة من المصدر</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="bi bi-truck"></i>
                        </div>
                        <div class="feature-content">
                            <h4 class="feature-title">توصيل سريع ومجاني</h4>
                            <p class="feature-description">توصيل مجاني لجميع أنحاء العراق خلال 24-48 ساعة</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="bi bi-headset"></i>
                        </div>
                        <div class="feature-content">
                            <h4 class="feature-title">خدمة عملاء متميزة</h4>
                            <p class="feature-description">فريق دعم متخصص متاح 24/7 لخدمتكم</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="bi bi-arrow-clockwise"></i>
                        </div>
                        <div class="feature-content">
                            <h4 class="feature-title">ضمان الإرجاع</h4>
                            <p class="feature-description">إمكانية الإرجاع والاستبدال خلال 14 يوم</p>
                        </div>
                    </div>
                </div>

                <!-- Call to Action -->
                <div class="about-cta">
                    <a href="about.php" class="btn btn-primary btn-lg">
                        <i class="bi bi-info-circle"></i>
                        <span>اعرف المزيد عنا</span>
                    </a>
                    <a href="contact.php" class="btn btn-outline btn-lg">
                        <i class="bi bi-telephone"></i>
                        <span>تواصل معنا</span>
                    </a>
                </div>
            </div>

            <!-- Visual Content -->
            <div class="about-visual">
                <!-- Main Image -->
                <div class="about-image-container">
                    <img src="assets/images/about-store-main.jpg" 
                         alt="متجر Care للعناية والجمال"
                         class="about-main-image"
                         loading="lazy">
                    
                    <!-- Floating Stats -->
                    <div class="floating-stats">
                        <div class="stat-item">
                            <div class="stat-number">5000+</div>
                            <div class="stat-label">عميل سعيد</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">منتج متنوع</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">3</div>
                            <div class="stat-label">سنوات خبرة</div>
                        </div>
                    </div>
                </div>

                <!-- Secondary Images -->
                <div class="about-gallery">
                    <div class="gallery-item">
                        <img src="assets/images/about-quality.jpg" 
                             alt="جودة المنتجات"
                             class="gallery-image"
                             loading="lazy">
                        <div class="gallery-overlay">
                            <span class="gallery-text">جودة عالية</span>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="assets/images/about-delivery.jpg" 
                             alt="خدمة التوصيل"
                             class="gallery-image"
                             loading="lazy">
                        <div class="gallery-overlay">
                            <span class="gallery-text">توصيل سريع</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Brand Values -->
        <div class="brand-values">
            <div class="values-header text-center">
                <h3 class="values-title">قيمنا ومبادئنا</h3>
                <p class="values-subtitle">نلتزم بأعلى معايير الجودة والخدمة</p>
            </div>

            <div class="values-grid grid grid-cols-4">
                <div class="value-card">
                    <div class="value-icon">
                        <i class="bi bi-heart"></i>
                    </div>
                    <h4 class="value-title">الشغف</h4>
                    <p class="value-description">نحب ما نقوم به ونسعى للتميز في كل تفصيل</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">
                        <i class="bi bi-star"></i>
                    </div>
                    <h4 class="value-title">الجودة</h4>
                    <p class="value-description">نختار أفضل المنتجات من أرقى العلامات التجارية</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <h4 class="value-title">العملاء أولاً</h4>
                    <p class="value-description">رضا عملائنا هو هدفنا الأول والأهم</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">
                        <i class="bi bi-lightning"></i>
                    </div>
                    <h4 class="value-title">الابتكار</h4>
                    <p class="value-description">نواكب أحدث الاتجاهات في عالم العناية والجمال</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Store Section Styles -->
<style>
/* About Store Section Specific Styles */
.about-store-section {
    background-color: var(--bg-primary);
    padding: var(--spacing-5xl) 0;
    position: relative;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
    margin-bottom: var(--spacing-5xl);
}

/* Text Content */
.about-text {
    padding: var(--spacing-xl);
}

.about-header {
    margin-bottom: var(--spacing-2xl);
}

.about-label {
    display: inline-block;
    background: var(--primary-gradient);
    color: var(--text-light);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    margin-bottom: var(--spacing-md);
}

.about-title {
    font-size: var(--text-4xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    line-height: var(--leading-tight);
}

.about-description {
    margin-bottom: var(--spacing-2xl);
}

.lead-text {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--spacing-lg);
    font-weight: var(--font-medium);
}

.about-description p {
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--spacing-md);
}

/* Store Features */
.store-features {
    margin-bottom: var(--spacing-2xl);
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal);
}

.feature-item:hover {
    background-color: var(--bg-light);
    transform: translateX(-4px);
}

.feature-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    color: var(--text-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
}

.feature-content {
    flex: 1;
}

.feature-title {
    font-size: var(--text-lg);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-semibold);
}

.feature-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-normal);
}

/* Call to Action */
.about-cta {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* Visual Content */
.about-visual {
    position: relative;
}

.about-image-container {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.about-main-image {
    width: 100%;
    height: 500px;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.about-image-container:hover .about-main-image {
    transform: scale(1.05);
}

/* Floating Stats */
.floating-stats {
    position: absolute;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    display: flex;
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* Gallery */
.about-gallery {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.gallery-item {
    position: relative;
    border-radius: var(--radius-md);
    overflow: hidden;
    height: 150px;
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-item:hover .gallery-image {
    transform: scale(1.1);
}

.gallery-text {
    color: var(--text-light);
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
}

/* Brand Values */
.brand-values {
    background-color: var(--bg-secondary);
    padding: var(--spacing-4xl);
    border-radius: var(--radius-lg);
}

.values-header {
    margin-bottom: var(--spacing-3xl);
}

.values-title {
    font-size: var(--text-3xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.values-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
}

.values-grid {
    gap: var(--spacing-xl);
}

.value-card {
    text-align: center;
    padding: var(--spacing-xl);
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.value-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    color: var(--text-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-3xl);
    margin: 0 auto var(--spacing-lg);
}

.value-title {
    font-size: var(--text-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-semibold);
}

.value-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-3xl);
    }
    
    .values-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .floating-stats {
        position: static;
        margin-top: var(--spacing-lg);
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .about-store-section {
        padding: var(--spacing-4xl) 0;
    }
    
    .about-text {
        padding: 0;
    }
    
    .about-title {
        font-size: var(--text-3xl);
    }
    
    .about-cta {
        flex-direction: column;
    }
    
    .values-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .floating-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .about-main-image {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .values-title {
        font-size: var(--text-2xl);
    }
    
    .value-icon {
        width: 60px;
        height: 60px;
        font-size: var(--text-2xl);
    }
}
</style>
