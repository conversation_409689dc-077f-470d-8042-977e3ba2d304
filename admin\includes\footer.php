    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // Toggle Sidebar for Mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const body = document.body;

            if (sidebar) {
                sidebar.classList.toggle('show');

                // Add/remove body class to prevent scrolling when sidebar is open on mobile
                if (window.innerWidth <= 768) {
                    body.classList.toggle('sidebar-open', sidebar.classList.contains('show'));
                }
            }
        }

        // Toggle Sidebar for Desktop - Complete Hide/Show with Enhanced Animation
        function toggleSidebarDesktop() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const toggleBtn = document.getElementById('sidebarToggleBtn');
            const toggleIcon = document.getElementById('sidebarToggleIcon');

            if (sidebar && mainContent) {
                const isCurrentlyHidden = sidebar.classList.contains('hidden');

                // Add transitioning class for smooth animation
                mainContent.classList.add('transitioning');

                // Toggle classes for complete hide/show
                sidebar.classList.toggle('hidden');
                mainContent.classList.toggle('full-width');

                // Update button appearance with enhanced styling
                if (toggleBtn) {
                    // Add ripple effect
                    createRippleEffect(toggleBtn);

                    if (isCurrentlyHidden) {
                        // Sidebar is being shown
                        toggleBtn.classList.remove('btn-outline-primary');
                        toggleBtn.classList.add('btn-outline-secondary');
                        toggleBtn.title = 'إخفاء الشريط الجانبي';
                    } else {
                        // Sidebar is being hidden
                        toggleBtn.classList.remove('btn-outline-secondary');
                        toggleBtn.classList.add('btn-outline-primary');
                        toggleBtn.title = 'إظهار الشريط الجانبي';
                    }
                }

                // Update icon with rotation animation
                if (toggleIcon) {
                    toggleIcon.style.transform = 'rotate(180deg)';
                    setTimeout(() => {
                        if (isCurrentlyHidden) {
                            // Sidebar is being shown
                            toggleIcon.className = 'bi bi-layout-sidebar-inset-reverse';
                        } else {
                            // Sidebar is being hidden
                            toggleIcon.className = 'bi bi-layout-sidebar-inset';
                        }
                        toggleIcon.style.transform = 'rotate(0deg)';
                    }, 200);
                }

                // Store sidebar state in localStorage
                localStorage.setItem('sidebarHidden', !isCurrentlyHidden);

                // Enhanced visual feedback with bounce animation
                if (toggleBtn) {
                    toggleBtn.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        toggleBtn.style.transform = 'scale(1.1)';
                        setTimeout(() => {
                            toggleBtn.style.transform = 'scale(1)';
                        }, 100);
                    }, 100);
                }

                // Remove transitioning class after animation completes
                setTimeout(() => {
                    mainContent.classList.remove('transitioning');
                }, 500);

                // Show enhanced toast feedback with icon
                const message = isCurrentlyHidden ?
                    '<i class="bi bi-layout-sidebar-inset-reverse me-2"></i>تم إظهار الشريط الجانبي' :
                    '<i class="bi bi-layout-sidebar-inset me-2"></i>تم إخفاء الشريط الجانبي';
                if (typeof showToast === 'function') {
                    showToast(message, 'info');
                }

                // Add subtle page shake effect for feedback
                document.body.style.animation = 'subtle-shake 0.3s ease-in-out';
                setTimeout(() => {
                    document.body.style.animation = '';
                }, 300);
            }
        }

        // Create ripple effect for button clicks
        function createRippleEffect(button) {
            const ripple = document.createElement('span');
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = '50%';
            ripple.style.top = '50%';
            ripple.style.transform = 'translate(-50%, -50%)';
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255, 255, 255, 0.3)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.pointerEvents = 'none';

            button.style.position = 'relative';
            button.style.overflow = 'hidden';
            button.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggleBtns = document.querySelectorAll('[onclick="toggleSidebar()"]');

            if (window.innerWidth <= 768 && sidebar && sidebar.classList.contains('show')) {
                let clickedToggleBtn = false;
                toggleBtns.forEach(btn => {
                    if (btn && btn.contains(e.target)) {
                        clickedToggleBtn = true;
                    }
                });

                if (!sidebar.contains(e.target) && !clickedToggleBtn) {
                    sidebar.classList.remove('show');
                    document.body.classList.remove('sidebar-open');
                }
            }
        });

        // Restore sidebar state on page load
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const toggleBtn = document.getElementById('sidebarToggleBtn');
            const toggleIcon = document.getElementById('sidebarToggleIcon');
            const sidebarHidden = localStorage.getItem('sidebarHidden') === 'true';

            if (sidebarHidden && window.innerWidth > 768 && sidebar && mainContent) {
                sidebar.classList.add('hidden');
                mainContent.classList.add('full-width');

                // Update button state
                if (toggleBtn) {
                    toggleBtn.classList.remove('btn-outline-secondary');
                    toggleBtn.classList.add('btn-outline-primary');
                    toggleBtn.title = 'إظهار الشريط الجانبي';
                }

                // Update icon
                if (toggleIcon) {
                    toggleIcon.className = 'bi bi-layout-sidebar-inset';
                }
            }
        });
        
        // Auto-refresh notifications
        function refreshNotifications() {
            fetch('ajax/notifications.php')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(text => {
                    try {
                        const data = JSON.parse(text);
                        if (data.error) {
                            console.error('Server error:', data.message);
                            return;
                        }

                        updateNotificationBadge('a[href="orders.php"]', data.pending_orders);
                        updateNotificationBadge('a[href="reviews.php"]', data.pending_reviews);
                        updateNotificationBadge('.btn[data-bs-toggle="dropdown"]', data.pending_orders + data.pending_reviews);
                    } catch (parseError) {
                        console.error('JSON Parse Error:', parseError);
                        console.error('Response text:', text);
                    }
                })
                .catch(error => console.error('Error refreshing notifications:', error));
        }

        // Helper function to update notification badges without duplicates
        function updateNotificationBadge(selector, count) {
            const element = document.querySelector(selector);
            if (!element) return;

            // Remove existing badge
            const existingBadge = element.querySelector('.notification-badge');
            if (existingBadge) {
                existingBadge.remove();
            }

            // Add new badge if count > 0
            if (count > 0) {
                const badge = document.createElement('span');
                badge.className = 'notification-badge';
                badge.textContent = count;
                element.appendChild(badge);
            }
        }
        
        // Refresh notifications every 30 seconds
        setInterval(refreshNotifications, 30000);
        
        // Professional Toast Messages
        function showToast(message, type = 'info', duration = 5000) {
            const toastContainer = document.getElementById('toastContainer') || createToastContainer();

            const icons = {
                success: 'bi-check-circle-fill',
                error: 'bi-exclamation-triangle-fill',
                warning: 'bi-exclamation-triangle-fill',
                info: 'bi-info-circle-fill'
            };

            const colors = {
                success: 'success',
                error: 'danger',
                warning: 'warning',
                info: 'info'
            };

            const toast = document.createElement('div');
            toast.className = `toast align-items-center border-0 shadow-lg`;
            toast.setAttribute('role', 'alert');
            toast.style.cssText = `
                background: linear-gradient(135deg,
                    ${type === 'success' ? '#28a745, #20c997' :
                      type === 'error' ? '#dc3545, #e74c3c' :
                      type === 'warning' ? '#ffc107, #ff9500' :
                      '#17a2b8, #20c997'});
                color: white;
                border-radius: 12px;
                margin-bottom: 12px;
                min-width: 300px;
                backdrop-filter: blur(10px);
            `;

            toast.innerHTML = `
                <div class="d-flex align-items-center p-3">
                    <i class="bi ${icons[type] || icons.info} me-3" style="font-size: 20px;"></i>
                    <div class="toast-body flex-grow-1" style="font-weight: 500; font-size: 14px;">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white ms-2" data-bs-dismiss="toast"
                            style="font-size: 12px; opacity: 0.8;" aria-label="إغلاق"></button>
                </div>
            `;

            toastContainer.appendChild(toast);

            const bsToast = new bootstrap.Toast(toast, {
                delay: duration,
                autohide: true
            });
            bsToast.show();

            // Add animation
            toast.style.transform = 'translateX(100%)';
            toast.style.transition = 'transform 0.3s ease-out';
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            toast.addEventListener('hidden.bs.toast', () => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => toast.remove(), 300);
            });
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1060';
            document.body.appendChild(container);
            return container;
        }

        // Professional Confirmation Dialog
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟', title = 'تأكيد الحذف', itemName = '') {
            return new Promise((resolve) => {
                const modalId = 'confirmDeleteModal_' + Date.now();
                const modal = document.createElement('div');
                modal.className = 'modal fade modal-professional';
                modal.id = modalId;
                modal.setAttribute('tabindex', '-1');
                modal.setAttribute('aria-hidden', 'true');

                const fullMessage = itemName ?
                    `${message}<br><br><strong class="text-danger">العنصر: ${itemName}</strong><br><small class="text-muted">هذا الإجراء لا يمكن التراجع عنه</small>` :
                    `${message}<br><small class="text-muted">هذا الإجراء لا يمكن التراجع عنه</small>`;

                modal.innerHTML = `
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title d-flex align-items-center">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    ${title}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body text-center py-4">
                                <div class="confirmation-professional">
                                    <i class="bi bi-trash3-fill icon danger"></i>
                                    <div class="title">${title}</div>
                                    <div class="message">${fullMessage}</div>
                                    <div class="actions">
                                        <button type="button" class="btn btn-professional btn-danger" id="confirmBtn_${modalId}">
                                            <i class="bi bi-trash3"></i>
                                            نعم، احذف
                                        </button>
                                        <button type="button" class="btn btn-professional btn-outline-secondary" data-bs-dismiss="modal">
                                            <i class="bi bi-x-circle"></i>
                                            إلغاء
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();

                document.getElementById(`confirmBtn_${modalId}`).addEventListener('click', () => {
                    bsModal.hide();
                    resolve(true);
                });

                modal.addEventListener('hidden.bs.modal', () => {
                    if (!modal.dataset.confirmed) {
                        resolve(false);
                    }
                    modal.remove();
                });

                document.getElementById(`confirmBtn_${modalId}`).addEventListener('click', () => {
                    modal.dataset.confirmed = 'true';
                });
            });
        }

        // Professional Success Confirmation
        function showSuccessConfirmation(message, title = 'تم بنجاح!', duration = 3000) {
            const modalId = 'successModal_' + Date.now();
            const modal = document.createElement('div');
            modal.className = 'modal fade modal-professional';
            modal.id = modalId;
            modal.setAttribute('tabindex', '-1');
            modal.setAttribute('aria-hidden', 'true');

            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered modal-sm">
                    <div class="modal-content">
                        <div class="modal-body text-center py-4">
                            <div class="confirmation-professional">
                                <i class="bi bi-check-circle-fill icon success"></i>
                                <div class="title text-success">${title}</div>
                                <div class="message">${message}</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            setTimeout(() => {
                bsModal.hide();
            }, duration);

            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        }

        // Professional Error Confirmation
        function showErrorConfirmation(message, title = 'حدث خطأ!') {
            const modalId = 'errorModal_' + Date.now();
            const modal = document.createElement('div');
            modal.className = 'modal fade modal-professional';
            modal.id = modalId;
            modal.setAttribute('tabindex', '-1');
            modal.setAttribute('aria-hidden', 'true');

            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title d-flex align-items-center">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                ${title}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center py-4">
                            <div class="confirmation-professional">
                                <i class="bi bi-x-circle-fill icon danger"></i>
                                <div class="title text-danger">${title}</div>
                                <div class="message">${message}</div>
                                <div class="actions">
                                    <button type="button" class="btn btn-professional btn-primary" data-bs-dismiss="modal">
                                        <i class="bi bi-check"></i>
                                        حسناً
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        }
        
        // Auto-save form data
        function autoSaveForm(formId, storageKey) {
            const form = document.getElementById(formId);
            if (!form) return;
            
            // Load saved data
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                const data = JSON.parse(savedData);
                Object.keys(data).forEach(key => {
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field && field.type !== 'file') {
                        field.value = data[key];
                    }
                });
            }
            
            // Save data on change
            form.addEventListener('input', function(e) {
                const formData = new FormData(form);
                const data = {};
                for (let [key, value] of formData.entries()) {
                    if (e.target.type !== 'file') {
                        data[key] = value;
                    }
                }
                localStorage.setItem(storageKey, JSON.stringify(data));
            });
            
            // Clear saved data on successful submit
            form.addEventListener('submit', function() {
                setTimeout(() => {
                    localStorage.removeItem(storageKey);
                }, 1000);
            });
        }
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
        
        // Initialize popovers
        document.addEventListener('DOMContentLoaded', function() {
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function(popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        });
        
        // Format numbers
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }
        
        // Format currency - محدثة لعرض أرقام صحيحة مع العملة المختصرة
        function formatCurrency(amount) {
            // تحويل إلى رقم صحيح
            const wholeAmount = Math.round(amount);
            return new Intl.NumberFormat('ar-SA', {
                style: 'decimal',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(wholeAmount) + ' <span class="currency-symbol">د.ع</span>';
        }
        
        // Copy to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('تم النسخ بنجاح', 'success');
            }).catch(() => {
                showToast('فشل في النسخ', 'error');
            });
        }
        
        // Export table to CSV
        function exportTableToCSV(tableId, filename) {
            const table = document.getElementById(tableId);
            if (!table) return;
            
            let csv = [];
            const rows = table.querySelectorAll('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = [];
                const cols = rows[i].querySelectorAll('td, th');
                
                for (let j = 0; j < cols.length; j++) {
                    let data = cols[j].innerText.replace(/(\r\n|\n|\r)/gm, '').replace(/(\s\s)/gm, ' ');
                    data = data.replace(/"/g, '""');
                    row.push('"' + data + '"');
                }
                csv.push(row.join(','));
            }
            
            const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
            const downloadLink = document.createElement('a');
            downloadLink.download = filename;
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
        
        // Print page
        function printPage() {
            window.print();
        }
        
        // Back to top button
        window.addEventListener('scroll', function() {
            const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            const backToTop = document.getElementById('backToTop');
            
            if (scrollTop > 300) {
                if (!backToTop) {
                    createBackToTopButton();
                }
            } else if (backToTop) {
                backToTop.remove();
            }
        });
        
        function createBackToTopButton() {
            const button = document.createElement('button');
            button.id = 'backToTop';
            button.className = 'btn btn-primary position-fixed';
            button.style.cssText = 'bottom: 20px; left: 20px; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;';
            button.innerHTML = '<i class="bi bi-arrow-up"></i>';
            button.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
            document.body.appendChild(button);
        }

        // Mark all notifications as read
        function markAllNotificationsRead() {
            const markAllBtn = document.getElementById('markAllReadBtn');
            if (markAllBtn) {
                markAllBtn.disabled = true;
                markAllBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>جاري التحديث...';
            }

            fetch('ajax/mark_notifications_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');

                    // Hide all notification badges
                    const badges = document.querySelectorAll('.notification-badge');
                    badges.forEach(badge => {
                        badge.style.opacity = '0';
                        setTimeout(() => badge.remove(), 300);
                    });

                    // Update notification dropdown
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showToast(data.message || 'حدث خطأ أثناء تحديث الإشعارات', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('حدث خطأ في الاتصال', 'error');
            })
            .finally(() => {
                if (markAllBtn) {
                    markAllBtn.disabled = false;
                    markAllBtn.innerHTML = '<i class="bi bi-check-all me-1"></i>تحديد الكل كمقروء';
                }
            });
        }

        // View all notifications (now navigates to real page)
        function viewAllNotifications() {
            window.location.href = 'notifications.php';
        }
    </script>
</body>
</html>
