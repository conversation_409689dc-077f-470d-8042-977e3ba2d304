/**
 * Professional Homepage Interactions - Arabic E-commerce
 * Modern JavaScript for carousel, sliders, transitions, and cart functionality
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description Complete JavaScript functionality for the rebuilt homepage
 */

document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // ========================================================================
    // GLOBAL VARIABLES AND CONFIGURATION
    // ========================================================================
    
    const CONFIG = {
        carousel: {
            autoAdvanceTime: 5000,
            transitionDuration: 500
        },
        animations: {
            observerThreshold: 0.1,
            observerRootMargin: '0px 0px -50px 0px'
        },
        toast: {
            duration: 5000,
            animationDelay: 100
        }
    };

    // ========================================================================
    // UTILITY FUNCTIONS
    // ========================================================================
    
    /**
     * Debounce function to limit function calls
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Check if element is in viewport
     */
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // ========================================================================
    // TOAST NOTIFICATION SYSTEM
    // ========================================================================
    
    class ToastManager {
        constructor() {
            this.container = this.createContainer();
        }

        createContainer() {
            let container = document.getElementById('toastContainer');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toastContainer';
                container.className = 'toast-container';
                document.body.appendChild(container);
            }
            return container;
        }

        show(message, type = 'info', duration = CONFIG.toast.duration) {
            const toast = this.createToast(message, type);
            this.container.appendChild(toast);
            
            // Show animation
            setTimeout(() => toast.classList.add('show'), CONFIG.toast.animationDelay);
            
            // Auto remove
            setTimeout(() => this.remove(toast), duration);
            
            return toast;
        }

        createToast(message, type) {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            
            const iconMap = {
                success: 'check-circle',
                error: 'exclamation-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };
            
            toast.innerHTML = `
                <div class="toast-content">
                    <i class="bi bi-${iconMap[type] || 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="toast-close" onclick="toastManager.remove(this.parentElement)">
                    <i class="bi bi-x"></i>
                </button>
            `;
            
            return toast;
        }

        remove(toast) {
            if (toast && toast.parentElement) {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.parentElement.removeChild(toast);
                    }
                }, 300);
            }
        }
    }

    // Initialize toast manager
    window.toastManager = new ToastManager();

    // ========================================================================
    // SHOPPING CART FUNCTIONALITY
    // ========================================================================
    
    class ShoppingCart {
        constructor() {
            this.items = this.loadCart();
            this.bindEvents();
        }

        loadCart() {
            try {
                return JSON.parse(localStorage.getItem('cart') || '[]');
            } catch (e) {
                console.error('Error loading cart:', e);
                return [];
            }
        }

        saveCart() {
            try {
                localStorage.setItem('cart', JSON.stringify(this.items));
                this.updateCartUI();
            } catch (e) {
                console.error('Error saving cart:', e);
            }
        }

        addItem(productId, productName, productPrice, quantity = 1) {
            const existingItem = this.items.find(item => item.id === productId);
            
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                this.items.push({
                    id: productId,
                    name: productName,
                    price: parseFloat(productPrice),
                    quantity: quantity,
                    addedAt: new Date().toISOString()
                });
            }
            
            this.saveCart();
            return true;
        }

        removeItem(productId) {
            this.items = this.items.filter(item => item.id !== productId);
            this.saveCart();
        }

        updateQuantity(productId, quantity) {
            const item = this.items.find(item => item.id === productId);
            if (item) {
                item.quantity = Math.max(0, quantity);
                if (item.quantity === 0) {
                    this.removeItem(productId);
                } else {
                    this.saveCart();
                }
            }
        }

        getTotal() {
            return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
        }

        getItemCount() {
            return this.items.reduce((count, item) => count + item.quantity, 0);
        }

        updateCartUI() {
            // Update cart counter in header
            const cartCounters = document.querySelectorAll('.cart-counter');
            const itemCount = this.getItemCount();
            
            cartCounters.forEach(counter => {
                counter.textContent = itemCount;
                counter.style.display = itemCount > 0 ? 'block' : 'none';
            });

            // Update cart total
            const cartTotals = document.querySelectorAll('.cart-total');
            const total = this.getTotal();
            
            cartTotals.forEach(totalElement => {
                totalElement.textContent = `${total.toLocaleString()} د.ع`;
            });
        }

        bindEvents() {
            // Add to cart buttons
            document.addEventListener('click', (e) => {
                if (e.target.closest('.btn-add-cart')) {
                    e.preventDefault();
                    this.handleAddToCart(e.target.closest('.btn-add-cart'));
                }
            });
        }

        handleAddToCart(button) {
            const productId = button.dataset.productId;
            const productName = button.dataset.productName;
            const productPrice = button.dataset.productPrice;
            
            if (!productId || !productName || !productPrice) {
                toastManager.show('خطأ في بيانات المنتج', 'error');
                return;
            }

            // Show loading state
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class="bi bi-arrow-repeat"></i> <span>جاري الإضافة...</span>';
            button.disabled = true;
            
            // Simulate API call delay
            setTimeout(() => {
                const success = this.addItem(productId, productName, productPrice);
                
                if (success) {
                    button.innerHTML = '<i class="bi bi-check"></i> <span>تمت الإضافة</span>';
                    button.style.backgroundColor = 'var(--success-color)';
                    
                    toastManager.show(`تم إضافة "${productName}" إلى السلة بنجاح`, 'success');
                    
                    // Add celebration effect
                    this.createCelebrationEffect(button);
                } else {
                    button.innerHTML = '<i class="bi bi-x"></i> <span>فشل في الإضافة</span>';
                    button.style.backgroundColor = 'var(--accent-color)';
                    
                    toastManager.show('فشل في إضافة المنتج إلى السلة', 'error');
                }
                
                // Reset button after 2 seconds
                setTimeout(() => {
                    button.innerHTML = originalContent;
                    button.style.backgroundColor = '';
                    button.disabled = false;
                }, 2000);
            }, 1000);
        }

        createCelebrationEffect(button) {
            const rect = button.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            for (let i = 0; i < 8; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: fixed;
                    width: 6px;
                    height: 6px;
                    background: var(--warning-color);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 9999;
                    left: ${centerX}px;
                    top: ${centerY}px;
                `;
                
                document.body.appendChild(particle);
                
                // Animate particle
                const angle = (i / 8) * Math.PI * 2;
                const distance = 100 + Math.random() * 50;
                const duration = 800 + Math.random() * 400;
                
                particle.animate([
                    { transform: 'translate(0, 0) scale(1)', opacity: 1 },
                    { 
                        transform: `translate(${Math.cos(angle) * distance}px, ${Math.sin(angle) * distance}px) scale(0)`, 
                        opacity: 0 
                    }
                ], {
                    duration: duration,
                    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
                }).onfinish = () => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                };
            }
        }
    }

    // Initialize shopping cart
    window.shoppingCart = new ShoppingCart();

    // ========================================================================
    // SCROLL ANIMATIONS AND INTERSECTION OBSERVER
    // ========================================================================
    
    class ScrollAnimations {
        constructor() {
            this.initIntersectionObserver();
            this.initScrollEffects();
        }

        initIntersectionObserver() {
            const observerOptions = {
                threshold: CONFIG.animations.observerThreshold,
                rootMargin: CONFIG.animations.observerRootMargin
            };

            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                        // Unobserve after animation to improve performance
                        this.observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe all animation elements
            document.querySelectorAll('.fade-in-up, .fade-in, .slide-in-left, .slide-in-right').forEach(el => {
                this.observer.observe(el);
            });
        }

        initScrollEffects() {
            // Back to top button
            this.initBackToTop();
            
            // Parallax effects for hero section
            this.initParallaxEffects();
        }

        initBackToTop() {
            const backToTopButton = document.getElementById('backToTop');
            if (!backToTopButton) return;

            const toggleBackToTop = debounce(() => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.add('visible');
                } else {
                    backToTopButton.classList.remove('visible');
                }
            }, 100);

            window.addEventListener('scroll', toggleBackToTop);
            
            backToTopButton.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        initParallaxEffects() {
            const heroSection = document.querySelector('.hero-section');
            if (!heroSection) return;

            const handleParallax = debounce(() => {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;
                
                heroSection.style.transform = `translateY(${rate}px)`;
            }, 16); // ~60fps

            window.addEventListener('scroll', handleParallax);
        }
    }

    // Initialize scroll animations
    new ScrollAnimations();

    // ========================================================================
    // SMOOTH SCROLLING FOR ANCHOR LINKS
    // ========================================================================
    
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            
            if (target) {
                const headerHeight = document.querySelector('header')?.offsetHeight || 0;
                const targetPosition = target.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // ========================================================================
    // NEWSLETTER FORM HANDLING
    // ========================================================================
    
    const newsletterForm = document.getElementById('newsletterForm');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('.newsletter-input').value;
            const button = this.querySelector('.btn-newsletter');
            const originalContent = button.innerHTML;
            
            // Validate email
            if (!isValidEmail(email)) {
                toastManager.show('يرجى إدخال بريد إلكتروني صحيح', 'error');
                return;
            }
            
            // Show loading state
            button.innerHTML = '<i class="bi bi-arrow-repeat"></i> <span>جاري الاشتراك...</span>';
            button.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                button.innerHTML = '<i class="bi bi-check"></i> <span>تم الاشتراك بنجاح!</span>';
                button.style.backgroundColor = 'var(--success-color)';
                
                toastManager.show('تم الاشتراك في النشرة الإخبارية بنجاح!', 'success');
                
                // Reset form
                this.reset();
                
                // Reset button after 3 seconds
                setTimeout(() => {
                    button.innerHTML = originalContent;
                    button.style.backgroundColor = '';
                    button.disabled = false;
                }, 3000);
            }, 1500);
        });
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // ========================================================================
    // QUICK VIEW AND WISHLIST FUNCTIONALITY
    // ========================================================================
    
    // Quick view buttons
    document.addEventListener('click', (e) => {
        if (e.target.closest('.btn-quick-view')) {
            e.preventDefault();
            const productId = e.target.closest('.btn-quick-view').dataset.productId;
            handleQuickView(productId);
        }
    });

    // Wishlist buttons
    document.addEventListener('click', (e) => {
        if (e.target.closest('.btn-add-wishlist')) {
            e.preventDefault();
            const button = e.target.closest('.btn-add-wishlist');
            const productId = button.dataset.productId;
            handleWishlistToggle(button, productId);
        }
    });

    function handleQuickView(productId) {
        // Implement quick view modal functionality
        toastManager.show('سيتم إضافة المعاينة السريعة قريباً', 'info');
        console.log('Quick view for product:', productId);
    }

    function handleWishlistToggle(button, productId) {
        button.classList.toggle('active');
        
        if (button.classList.contains('active')) {
            button.innerHTML = '<i class="bi bi-heart-fill"></i>';
            button.style.color = 'var(--accent-color)';
            toastManager.show('تم إضافة المنتج إلى المفضلة', 'success');
        } else {
            button.innerHTML = '<i class="bi bi-heart"></i>';
            button.style.color = '';
            toastManager.show('تم إزالة المنتج من المفضلة', 'info');
        }
    }

    // ========================================================================
    // PERFORMANCE MONITORING
    // ========================================================================
    
    // Monitor page load performance
    window.addEventListener('load', () => {
        if ('performance' in window) {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
        }
    });

    // Initialize cart UI on page load
    if (window.shoppingCart) {
        window.shoppingCart.updateCartUI();
    }

    console.log('Homepage interactions initialized successfully');
});
