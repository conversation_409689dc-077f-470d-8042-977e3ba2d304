<?php
/**
 * Professional Why Choose Us Section Component
 * Modern Arabic E-commerce Features and Advantages Display
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description Professional why choose us section with features and services
 */

// Define features and advantages
$features = [
    [
        'icon' => 'bi-shield-check',
        'title' => 'منتجات أصلية 100%',
        'description' => 'جميع منتجاتنا أصلية ومضمونة من المصدر مع شهادات الجودة والأصالة',
        'color' => 'success'
    ],
    [
        'icon' => 'bi-truck',
        'title' => 'توصيل سريع ومجاني',
        'description' => 'توصيل مجاني لجميع أنحاء العراق خلال 24-48 ساعة مع إمكانية التتبع',
        'color' => 'primary'
    ],
    [
        'icon' => 'bi-headset',
        'title' => 'خدمة عملاء متميزة',
        'description' => 'فريق دعم متخصص متاح 24/7 لخدمتكم والإجابة على جميع استفساراتكم',
        'color' => 'info'
    ],
    [
        'icon' => 'bi-arrow-clockwise',
        'title' => 'ضمان الإرجاع',
        'description' => 'إمكانية الإرجاع والاستبدال خلال 14 يوم مع ضمان استرداد المبلغ كاملاً',
        'color' => 'warning'
    ],
    [
        'icon' => 'bi-credit-card',
        'title' => 'دفع آمن ومرن',
        'description' => 'خيارات دفع متنوعة وآمنة مع إمكانية الدفع عند الاستلام',
        'color' => 'success'
    ],
    [
        'icon' => 'bi-award',
        'title' => 'جودة مضمونة',
        'description' => 'منتجات عالية الجودة من أفضل العلامات التجارية العالمية والمحلية',
        'color' => 'primary'
    ],
    [
        'icon' => 'bi-percent',
        'title' => 'عروض وخصومات',
        'description' => 'عروض حصرية وخصومات مستمرة مع برنامج نقاط الولاء للعملاء المميزين',
        'color' => 'accent'
    ],
    [
        'icon' => 'bi-people',
        'title' => 'مجتمع متنامي',
        'description' => 'انضم إلى أكثر من 10,000 عميل راضٍ واستفد من تجاربهم ونصائحهم',
        'color' => 'info'
    ]
];

// Statistics data
$statistics = [
    [
        'number' => '10,000+',
        'label' => 'عميل سعيد',
        'icon' => 'bi-people-fill'
    ],
    [
        'number' => '500+',
        'label' => 'منتج متنوع',
        'icon' => 'bi-box-seam'
    ],
    [
        'number' => '99%',
        'label' => 'رضا العملاء',
        'icon' => 'bi-emoji-smile'
    ],
    [
        'number' => '24/7',
        'label' => 'دعم العملاء',
        'icon' => 'bi-headset'
    ]
];
?>

<!-- Why Choose Us Section - Professional Design -->
<section class="section features-section" id="why-choose-us">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header text-center">
            <span class="section-label">مزايانا</span>
            <h2 class="section-title">لماذا يختارنا العملاء؟</h2>
            <p class="section-subtitle">نقدم لك أفضل تجربة تسوق مع خدمات متميزة وضمانات حقيقية</p>
        </div>

        <!-- Features Grid -->
        <div class="features-grid">
            <?php foreach ($features as $index => $feature): ?>
            <div class="feature-card fade-in-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                <div class="feature-icon-container <?php echo $feature['color']; ?>">
                    <i class="feature-icon <?php echo $feature['icon']; ?>"></i>
                </div>
                
                <div class="feature-content">
                    <h3 class="feature-title"><?php echo htmlspecialchars($feature['title']); ?></h3>
                    <p class="feature-description"><?php echo htmlspecialchars($feature['description']); ?></p>
                </div>
                
                <div class="feature-hover-effect"></div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Statistics Section -->
        <div class="statistics-section">
            <div class="statistics-header text-center">
                <h3 class="statistics-title">أرقام تتحدث عن نفسها</h3>
                <p class="statistics-subtitle">إنجازاتنا وثقة عملائنا بأرقام حقيقية</p>
            </div>
            
            <div class="statistics-grid">
                <?php foreach ($statistics as $index => $stat): ?>
                <div class="stat-card fade-in-up" style="animation-delay: <?php echo ($index + 8) * 0.1; ?>s;">
                    <div class="stat-icon">
                        <i class="<?php echo $stat['icon']; ?>"></i>
                    </div>
                    <div class="stat-number" data-target="<?php echo preg_replace('/[^0-9]/', '', $stat['number']); ?>">
                        <?php echo htmlspecialchars($stat['number']); ?>
                    </div>
                    <div class="stat-label"><?php echo htmlspecialchars($stat['label']); ?></div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Trust Badges -->
        <div class="trust-badges">
            <div class="trust-badges-header text-center">
                <h3 class="trust-title">شهادات الثقة والجودة</h3>
            </div>
            
            <div class="badges-grid">
                <div class="trust-badge">
                    <div class="badge-icon">
                        <i class="bi bi-shield-fill-check"></i>
                    </div>
                    <div class="badge-text">
                        <div class="badge-title">شهادة الأصالة</div>
                        <div class="badge-subtitle">منتجات أصلية مضمونة</div>
                    </div>
                </div>
                
                <div class="trust-badge">
                    <div class="badge-icon">
                        <i class="bi bi-award-fill"></i>
                    </div>
                    <div class="badge-text">
                        <div class="badge-title">جائزة الجودة</div>
                        <div class="badge-subtitle">أفضل متجر للعناية 2024</div>
                    </div>
                </div>
                
                <div class="trust-badge">
                    <div class="badge-icon">
                        <i class="bi bi-patch-check-fill"></i>
                    </div>
                    <div class="badge-text">
                        <div class="badge-title">موثق رسمياً</div>
                        <div class="badge-subtitle">مسجل في وزارة التجارة</div>
                    </div>
                </div>
                
                <div class="trust-badge">
                    <div class="badge-icon">
                        <i class="bi bi-heart-fill"></i>
                    </div>
                    <div class="badge-text">
                        <div class="badge-title">ثقة العملاء</div>
                        <div class="badge-subtitle">99% رضا العملاء</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="features-cta text-center">
            <h3 class="cta-title">جرب تجربة التسوق المتميزة</h3>
            <p class="cta-description">انضم إلى آلاف العملاء الراضين واكتشف الفرق معنا</p>
            <div class="cta-actions">
                <a href="products.php" class="btn btn-primary btn-lg">
                    <i class="bi bi-shop"></i>
                    <span>تسوق الآن</span>
                </a>
                <a href="about.php" class="btn btn-outline btn-lg">
                    <i class="bi bi-info-circle"></i>
                    <span>اعرف المزيد</span>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us Section Styles -->
<style>
/* Features Section Styles */
.features-section {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
    padding: var(--spacing-5xl) 0;
    position: relative;
    overflow: hidden;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(220, 38, 38, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-5xl);
}

/* Feature Card Styles */
.feature-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.feature-hover-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.feature-card:hover .feature-hover-effect {
    left: 100%;
}

/* Feature Icon */
.feature-icon-container {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    position: relative;
    transition: all var(--transition-normal);
}

.feature-icon-container.primary {
    background: var(--primary-gradient);
}

.feature-icon-container.success {
    background: var(--success-gradient);
}

.feature-icon-container.info {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.feature-icon-container.warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
}

.feature-icon-container.accent {
    background: var(--accent-gradient);
}

.feature-icon {
    font-size: var(--text-2xl);
    color: var(--text-light);
}

.feature-card:hover .feature-icon-container {
    transform: scale(1.1) rotate(5deg);
}

/* Feature Content */
.feature-content {
    position: relative;
    z-index: 2;
}

.feature-title {
    font-size: var(--text-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-semibold);
}

.feature-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
}

/* Statistics Section */
.statistics-section {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4xl);
    margin-bottom: var(--spacing-5xl);
    box-shadow: var(--shadow-lg);
}

.statistics-header {
    margin-bottom: var(--spacing-3xl);
}

.statistics-title {
    font-size: var(--text-3xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-bold);
}

.statistics-subtitle {
    font-size: var(--text-base);
    color: var(--text-secondary);
}

.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-2xl);
}

/* Stat Card */
.stat-card {
    text-align: center;
    padding: var(--spacing-xl);
    border-radius: var(--radius-md);
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-secondary) 100%);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    font-size: var(--text-2xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.stat-number {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
}

/* Trust Badges */
.trust-badges {
    margin-bottom: var(--spacing-5xl);
}

.trust-badges-header {
    margin-bottom: var(--spacing-3xl);
}

.trust-title {
    font-size: var(--text-2xl);
    color: var(--text-primary);
    font-weight: var(--font-semibold);
}

.badges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-normal);
}

.trust-badge:hover {
    box-shadow: var(--shadow-md);
    transform: translateX(-4px);
}

.badge-icon {
    width: 50px;
    height: 50px;
    background: var(--success-gradient);
    color: var(--text-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    flex-shrink: 0;
}

.badge-text {
    flex: 1;
}

.badge-title {
    font-size: var(--text-base);
    color: var(--text-primary);
    font-weight: var(--font-semibold);
    margin-bottom: var(--spacing-xs);
}

.badge-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* Features CTA */
.features-cta {
    background: var(--primary-gradient);
    color: var(--text-light);
    padding: var(--spacing-4xl);
    border-radius: var(--radius-lg);
    position: relative;
    overflow: hidden;
}

.features-cta::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

.cta-title {
    font-size: var(--text-3xl);
    margin-bottom: var(--spacing-md);
    position: relative;
    z-index: 2;
}

.cta-description {
    font-size: var(--text-lg);
    margin-bottom: var(--spacing-2xl);
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.cta-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
    position: relative;
    z-index: 2;
}

.features-cta .btn {
    background-color: var(--text-light);
    color: var(--primary-color);
    border-color: var(--text-light);
}

.features-cta .btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.features-cta .btn-outline {
    background-color: transparent;
    color: var(--text-light);
    border-color: var(--text-light);
}

.features-cta .btn-outline:hover {
    background-color: var(--text-light);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .statistics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .badges-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .statistics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    .badges-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .feature-icon-container {
        width: 60px;
        height: 60px;
    }
    
    .feature-icon {
        font-size: var(--text-xl);
    }
}

@media (max-width: 480px) {
    .statistics-grid {
        grid-template-columns: 1fr;
    }
    
    .trust-badge {
        flex-direction: column;
        text-align: center;
    }
    
    .stat-number {
        font-size: var(--text-3xl);
    }
    
    .statistics-title {
        font-size: var(--text-2xl);
    }
    
    .cta-title {
        font-size: var(--text-2xl);
    }
}
</style>

<!-- Features JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate statistics numbers
    function animateNumbers() {
        const statNumbers = document.querySelectorAll('.stat-number');
        
        statNumbers.forEach(stat => {
            const target = parseInt(stat.dataset.target) || 0;
            const text = stat.textContent;
            const hasPercent = text.includes('%');
            const hasPlus = text.includes('+');
            const hasSlash = text.includes('/');
            
            if (target > 0) {
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    
                    let displayValue = Math.floor(current).toLocaleString();
                    if (hasPercent) displayValue += '%';
                    if (hasPlus) displayValue += '+';
                    if (hasSlash) displayValue = text; // Keep original for 24/7
                    
                    stat.textContent = displayValue;
                }, 50);
            }
        });
    }
    
    // Intersection Observer for animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                if (entry.target.classList.contains('statistics-section')) {
                    animateNumbers();
                }
            }
        });
    }, { threshold: 0.5 });
    
    const statisticsSection = document.querySelector('.statistics-section');
    if (statisticsSection) {
        observer.observe(statisticsSection);
    }
    
    // Add hover effects to feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
