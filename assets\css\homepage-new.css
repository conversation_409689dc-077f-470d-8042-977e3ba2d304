/**
 * Professional Arabic E-commerce Homepage - Rebuilt from Scratch
 * Modern, Clean, and Error-Free Implementation
 * Optimized for RTL Arabic Layout with Professional Design
 *
 * @version 5.0
 * <AUTHOR> Development Team
 * @description Complete homepage rebuild with white background, black/gray text, rectangular buttons
 */

/* ==========================================================================
   CSS Custom Properties (Variables) for Professional Arabic E-commerce
   ========================================================================== */

:root {
    /* Brand Colors - Professional Arabic E-commerce */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);

    /* Secondary Colors */
    --secondary-color: #64748b;
    --secondary-dark: #475569;
    --secondary-light: #94a3b8;
    --accent-color: #dc2626;
    --success-color: #16a34a;
    --warning-color: #d97706;

    /* Text Colors - Professional Hierarchy */
    --text-primary: #000000;        /* Main headings - Pure black */
    --text-secondary: #333333;      /* Long paragraphs - Dark gray for eye comfort */
    --text-muted: #6b7280;         /* Supporting text */
    --text-light: #ffffff;         /* Light text on dark backgrounds */
    --text-disabled: #9ca3af;      /* Disabled states */

    /* Background Colors - Clean White Base */
    --bg-primary: #ffffff;         /* Main background - Pure white */
    --bg-secondary: #f9fafb;       /* Section backgrounds */
    --bg-light: #f3f4f6;          /* Card backgrounds */
    --bg-dark: #1f2937;           /* Dark sections */

    /* Shadows - Professional Depth */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius - Rectangular with Subtle Curves */
    --radius-sm: 8px;              /* Buttons and small elements */
    --radius-md: 12px;             /* Cards and containers */
    --radius-lg: 16px;             /* Large sections */
    --radius-none: 0px;            /* Rectangular elements */

    /* Spacing - Professional Scale */
    --spacing-xs: 0.25rem;         /* 4px */
    --spacing-sm: 0.5rem;          /* 8px */
    --spacing-md: 1rem;            /* 16px */
    --spacing-lg: 1.5rem;          /* 24px */
    --spacing-xl: 2rem;            /* 32px */
    --spacing-2xl: 3rem;           /* 48px */
    --spacing-3xl: 4rem;           /* 64px */
    --spacing-4xl: 5rem;           /* 80px */
    --spacing-5xl: 6rem;           /* 96px */

    /* Typography - Arabic-Optimized Fonts */
    --font-family-primary: 'Cairo', 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
    --font-family-secondary: 'Roboto', 'Arial', 'Helvetica', sans-serif;
    --font-family-display: 'Cairo', 'Georgia', serif;

    /* Font Sizes - Professional Scale */
    --text-xs: 0.75rem;            /* 12px */
    --text-sm: 0.875rem;           /* 14px */
    --text-base: 1rem;             /* 16px */
    --text-lg: 1.125rem;           /* 18px */
    --text-xl: 1.25rem;            /* 20px */
    --text-2xl: 1.5rem;            /* 24px */
    --text-3xl: 1.875rem;          /* 30px */
    --text-4xl: 2.25rem;           /* 36px */
    --text-5xl: 3rem;              /* 48px */
    --text-6xl: 3.75rem;           /* 60px */

    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;

    /* Line Heights - Optimized for Arabic */
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Transitions - Smooth and Professional */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* Z-Index Scale - Organized Layers */
    --z-base: 0;
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* Iraqi Dinar Currency */
    --currency-symbol: 'د.ع';
    --currency-font-size: 0.75rem;
}

/* ==========================================================================
   Base RTL Layout & Typography
   ========================================================================== */

body {
    direction: rtl;
    text-align: right;
    font-family: var(--font-family-primary);
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
    scroll-behavior: smooth;
    margin: 0;
    padding: 0;

    /* Enhanced Arabic Text Rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "kern" 1, "liga" 1;

    /* Arabic-specific optimizations */
    word-spacing: 0.05em;
    letter-spacing: 0.01em;
}

* {
    box-sizing: border-box;
}

/* ==========================================================================
   Typography System
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary);
    font-family: var(--font-family-primary);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin: 0 0 var(--spacing-md) 0;
}

h1 { font-size: var(--text-5xl); }
h2 { font-size: var(--text-4xl); }
h3 { font-size: var(--text-3xl); }
h4 { font-size: var(--text-2xl); }
h5 { font-size: var(--text-xl); }
h6 { font-size: var(--text-lg); }

p {
    color: var(--text-secondary);
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    margin: 0 0 var(--spacing-md) 0;
}

.text-muted {
    color: var(--text-muted);
}

.text-primary {
    color: var(--text-primary);
}

.text-secondary {
    color: var(--text-secondary);
}

/* ==========================================================================
   Button System - Rectangular Design
   ========================================================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-family: var(--font-family-primary);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    text-decoration: none;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-light);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-light);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--text-light);
}

/* ==========================================================================
   Container System
   ========================================================================== */

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-md);
}

/* ==========================================================================
   Section Separators - Professional Visual Hierarchy
   ========================================================================== */

.section-separator {
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--text-muted) 50%, transparent 100%);
    margin: var(--spacing-3xl) 0;
    opacity: 0.3;
}

.section {
    padding: var(--spacing-3xl) 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--text-4xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.section-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ==========================================================================
   Grid System - Responsive
   ========================================================================== */

.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* Responsive Grid */
@media (max-width: 1024px) {
    .grid-cols-6 { grid-template-columns: repeat(4, 1fr); }
    .grid-cols-4 { grid-template-columns: repeat(3, 1fr); }
}

@media (max-width: 768px) {
    .grid-cols-6 { grid-template-columns: repeat(2, 1fr); }
    .grid-cols-4 { grid-template-columns: repeat(2, 1fr); }
    .grid-cols-3 { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 480px) {
    .grid-cols-6,
    .grid-cols-4,
    .grid-cols-3,
    .grid-cols-2 {
        grid-template-columns: 1fr;
    }
}

/* ==========================================================================
   Hero Carousel - Rectangular Design
   ========================================================================== */

.hero-section {
    position: relative;
    width: 100%;
    height: 500px;
    overflow: hidden;
    background-color: var(--bg-secondary);
}

.hero-carousel {
    position: relative;
    width: 100%;
    height: 100%;
}

.carousel-slides {
    position: relative;
    width: 100%;
    height: 100%;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity var(--transition-slow);
}

.carousel-slide.active {
    opacity: 1;
}

.slide-image-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.slide-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-none);
}

.slide-content {
    position: absolute;
    top: 50%;
    right: var(--spacing-3xl);
    transform: translateY(-50%);
    z-index: 2;
    max-width: 500px;
}

.slide-title {
    font-size: var(--text-5xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.slide-subtitle {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.slide-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.btn-hero {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
}

/* Carousel Controls */
.carousel-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
}

.carousel-btn {
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.carousel-btn:hover {
    background-color: var(--primary-color);
    color: var(--text-light);
    transform: scale(1.1);
}

.carousel-btn-prev {
    right: var(--spacing-lg);
}

.carousel-btn-next {
    left: var(--spacing-lg);
}

/* Carousel Indicators */
.carousel-indicators {
    position: absolute;
    bottom: var(--spacing-lg);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--spacing-sm);
    z-index: 3;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.indicator.active,
.indicator:hover {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

/* ==========================================================================
   Product Cards - Professional Design
   ========================================================================== */

.product-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.product-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.product-image-container {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
    background-color: var(--bg-light);
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background-color: var(--accent-color);
    color: var(--text-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    z-index: 2;
}

.product-badge.featured {
    background-color: var(--warning-color);
}

.product-badge.new {
    background-color: var(--success-color);
}

.product-content {
    padding: var(--spacing-lg);
}

.product-title {
    font-size: var(--text-lg);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-semibold);
    line-height: var(--leading-snug);
}

.product-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: var(--leading-normal);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.price-current {
    font-size: var(--text-xl);
    color: var(--text-primary);
    font-weight: var(--font-bold);
}

.price-original {
    font-size: var(--text-base);
    color: var(--text-muted);
    text-decoration: line-through;
}

.currency {
    font-size: var(--currency-font-size);
    color: var(--text-muted);
    margin-right: var(--spacing-xs);
}

.product-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.btn-add-cart {
    flex: 1;
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.btn-add-cart:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-quick-view {
    background-color: transparent;
    color: var(--text-muted);
    border: 1px solid var(--text-muted);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-quick-view:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* ==========================================================================
   Category Cards - Professional Design
   ========================================================================== */

.category-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: hidden;
    text-align: center;
    padding: var(--spacing-2xl);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

.category-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.category-icon {
    font-size: var(--text-5xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    display: block;
}

.category-title {
    font-size: var(--text-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-semibold);
}

.category-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: var(--leading-normal);
}

.btn-explore {
    background-color: var(--secondary-color);
    color: var(--text-light);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-sm);
    text-decoration: none;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    transition: all var(--transition-normal);
    display: inline-block;
}

.btn-explore:hover {
    background-color: var(--secondary-dark);
    transform: translateY(-1px);
}

/* ==========================================================================
   Review Cards - Customer Testimonials
   ========================================================================== */

.review-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-2xl);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

.review-rating {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
    justify-content: center;
}

.star {
    color: var(--warning-color);
    font-size: var(--text-lg);
}

.star.empty {
    color: var(--text-disabled);
}

.review-text {
    font-size: var(--text-base);
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--spacing-lg);
    text-align: center;
    font-style: italic;
}

.review-author {
    text-align: center;
}

.author-name {
    font-size: var(--text-lg);
    color: var(--text-primary);
    font-weight: var(--font-semibold);
    margin-bottom: var(--spacing-xs);
}

.author-title {
    font-size: var(--text-sm);
    color: var(--text-muted);
}

/* ==========================================================================
   Influencer Cards - Brand Ambassadors
   ========================================================================== */

.influencer-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.influencer-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.influencer-image-container {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    background-color: var(--bg-light);
}

.influencer-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.influencer-card:hover .influencer-image {
    transform: scale(1.05);
}

.influencer-content {
    padding: var(--spacing-lg);
    text-align: center;
}

.influencer-name {
    font-size: var(--text-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-semibold);
}

.influencer-title {
    font-size: var(--text-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.influencer-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-normal);
    margin-bottom: var(--spacing-lg);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.influencer-rating {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

/* ==========================================================================
   Newsletter Section
   ========================================================================== */

.newsletter-section {
    background: var(--primary-gradient);
    color: var(--text-light);
    padding: var(--spacing-4xl) 0;
    text-align: center;
}

.newsletter-title {
    font-size: var(--text-4xl);
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.newsletter-subtitle {
    font-size: var(--text-lg);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    gap: var(--spacing-md);
}

.newsletter-input {
    flex: 1;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--text-base);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.newsletter-input:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

.btn-newsletter {
    background-color: var(--text-light);
    color: var(--primary-color);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-sm);
    font-weight: var(--font-semibold);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
}

.btn-newsletter:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
}

/* ==========================================================================
   Social Media Section
   ========================================================================== */

.social-section {
    background-color: var(--bg-secondary);
    padding: var(--spacing-3xl) 0;
    text-align: center;
}

.social-title {
    font-size: var(--text-3xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.social-links {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background-color: var(--bg-primary);
    border-radius: 50%;
    color: var(--text-muted);
    text-decoration: none;
    font-size: var(--text-2xl);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.social-link:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.social-link.facebook:hover {
    background-color: #1877f2;
    color: var(--text-light);
}

.social-link.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    color: var(--text-light);
}

.social-link.twitter:hover {
    background-color: #1da1f2;
    color: var(--text-light);
}

.social-link.youtube:hover {
    background-color: #ff0000;
    color: var(--text-light);
}

.social-link.whatsapp:hover {
    background-color: #25d366;
    color: var(--text-light);
}

/* ==========================================================================
   Why Choose Us Section
   ========================================================================== */

.features-section {
    padding: var(--spacing-4xl) 0;
    background-color: var(--bg-primary);
}

.feature-card {
    text-align: center;
    padding: var(--spacing-2xl);
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
    background-color: var(--bg-primary);
    box-shadow: var(--shadow-md);
    transform: translateY(-4px);
}

.feature-icon {
    font-size: var(--text-5xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    display: block;
}

.feature-title {
    font-size: var(--text-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-semibold);
}

.feature-description {
    font-size: var(--text-base);
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
}

/* ==========================================================================
   About Store Section
   ========================================================================== */

.about-section {
    padding: var(--spacing-4xl) 0;
    background-color: var(--bg-secondary);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.about-text {
    padding: var(--spacing-xl);
}

.about-title {
    font-size: var(--text-4xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.about-description {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--spacing-xl);
}

.about-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
}

/* ==========================================================================
   Responsive Design - Mobile First
   ========================================================================== */

@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-lg);
    }

    .slide-content {
        right: var(--spacing-xl);
        max-width: 400px;
    }

    .slide-title {
        font-size: var(--text-4xl);
    }

    .slide-subtitle {
        font-size: var(--text-lg);
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 400px;
    }

    .slide-content {
        right: var(--spacing-lg);
        max-width: 300px;
    }

    .slide-title {
        font-size: var(--text-3xl);
    }

    .slide-subtitle {
        font-size: var(--text-base);
    }

    .slide-actions {
        flex-direction: column;
    }

    .newsletter-form {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .social-links {
        gap: var(--spacing-md);
    }

    .section {
        padding: var(--spacing-2xl) 0;
    }

    .section-title {
        font-size: var(--text-3xl);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .hero-section {
        height: 300px;
    }

    .slide-content {
        right: var(--spacing-md);
        left: var(--spacing-md);
        max-width: none;
        text-align: center;
    }

    .slide-title {
        font-size: var(--text-2xl);
    }

    .slide-subtitle {
        font-size: var(--text-sm);
    }

    .btn-hero {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-base);
    }

    .product-image-container {
        height: 200px;
    }

    .section-title {
        font-size: var(--text-2xl);
    }

    .section-subtitle {
        font-size: var(--text-base);
    }
}

/* ==========================================================================
   Loading States and Animations
   ========================================================================== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in {
    animation: fadeIn 0.4s ease-out;
}

/* Loading Spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bg-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.hidden { display: none; }
.visible { display: block; }

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
