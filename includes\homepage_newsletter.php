<?php
/**
 * Professional Newsletter Section Component
 * Modern Arabic E-commerce Newsletter Subscription
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description Professional newsletter section with AJAX functionality
 */

// Newsletter benefits data
$newsletterBenefits = [
    [
        'icon' => 'bi-percent',
        'title' => 'عروض حصرية',
        'description' => 'احصل على خصومات تصل إلى 50% قبل الجميع'
    ],
    [
        'icon' => 'bi-bell',
        'title' => 'إشعارات المنتجات الجديدة',
        'description' => 'كن أول من يعرف عن المنتجات الجديدة'
    ],
    [
        'icon' => 'bi-gift',
        'title' => 'هدايا مجانية',
        'description' => 'عينات مجانية وهدايا مع كل طلب'
    ],
    [
        'icon' => 'bi-star',
        'title' => 'نصائح الجمال',
        'description' => 'نصائح وإرشادات من خبراء العناية والجمال'
    ]
];
?>

<!-- Newsletter Section - Professional Design -->
<section class="newsletter-section" id="newsletter">
    <div class="container">
        <!-- Background Elements -->
        <div class="newsletter-bg-elements">
            <div class="bg-circle bg-circle-1"></div>
            <div class="bg-circle bg-circle-2"></div>
            <div class="bg-circle bg-circle-3"></div>
        </div>
        
        <!-- Newsletter Content -->
        <div class="newsletter-content">
            <!-- Main Newsletter Form -->
            <div class="newsletter-main">
                <div class="newsletter-header text-center">
                    <div class="newsletter-icon">
                        <i class="bi bi-envelope-heart"></i>
                    </div>
                    <h2 class="newsletter-title">اشترك في النشرة الإخبارية</h2>
                    <p class="newsletter-subtitle">
                        احصل على آخر العروض والمنتجات الجديدة ونصائح الجمال مباشرة في بريدك الإلكتروني
                    </p>
                </div>
                
                <!-- Newsletter Form -->
                <form class="newsletter-form" id="newsletterForm">
                    <div class="form-group">
                        <div class="input-container">
                            <input type="email" 
                                   class="newsletter-input" 
                                   id="newsletterEmail"
                                   placeholder="أدخل بريدك الإلكتروني"
                                   required>
                            <div class="input-icon">
                                <i class="bi bi-envelope"></i>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn-newsletter">
                            <span class="btn-text">
                                <i class="bi bi-send"></i>
                                <span>اشتراك</span>
                            </span>
                            <span class="btn-loading" style="display: none;">
                                <i class="bi bi-arrow-repeat"></i>
                                <span>جاري الاشتراك...</span>
                            </span>
                            <span class="btn-success" style="display: none;">
                                <i class="bi bi-check-circle"></i>
                                <span>تم الاشتراك!</span>
                            </span>
                        </button>
                    </div>
                    
                    <!-- Privacy Notice -->
                    <div class="privacy-notice">
                        <label class="privacy-checkbox">
                            <input type="checkbox" id="privacyConsent" required>
                            <span class="checkmark"></span>
                            <span class="privacy-text">
                                أوافق على 
                                <a href="privacy.php" target="_blank">سياسة الخصوصية</a>
                                وأرغب في تلقي النشرة الإخبارية
                            </span>
                        </label>
                    </div>
                </form>
                
                <!-- Success Message -->
                <div class="newsletter-success" id="newsletterSuccess" style="display: none;">
                    <div class="success-icon">
                        <i class="bi bi-check-circle-fill"></i>
                    </div>
                    <h3 class="success-title">تم الاشتراك بنجاح!</h3>
                    <p class="success-message">
                        شكراً لك! تم تسجيل بريدك الإلكتروني بنجاح. ستصلك أحدث العروض والمنتجات الجديدة قريباً.
                    </p>
                </div>
            </div>
            
            <!-- Newsletter Benefits -->
            <div class="newsletter-benefits">
                <h3 class="benefits-title">ماذا ستحصل عليه؟</h3>
                <div class="benefits-grid">
                    <?php foreach ($newsletterBenefits as $index => $benefit): ?>
                    <div class="benefit-item fade-in-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                        <div class="benefit-icon">
                            <i class="<?php echo $benefit['icon']; ?>"></i>
                        </div>
                        <div class="benefit-content">
                            <h4 class="benefit-title"><?php echo htmlspecialchars($benefit['title']); ?></h4>
                            <p class="benefit-description"><?php echo htmlspecialchars($benefit['description']); ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- Newsletter Statistics -->
        <div class="newsletter-stats">
            <div class="stats-container">
                <div class="stat-item">
                    <div class="stat-number">15,000+</div>
                    <div class="stat-label">مشترك نشط</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">معدل الرضا</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2x</div>
                    <div class="stat-label">نشرة أسبوعياً</div>
                </div>
            </div>
        </div>
        
        <!-- Social Proof -->
        <div class="social-proof text-center">
            <p class="social-proof-text">
                <i class="bi bi-people-fill"></i>
                انضم إلى أكثر من <strong>15,000</strong> عميل راضٍ يتلقون عروضنا الحصرية
            </p>
        </div>
    </div>
</section>

<!-- Newsletter Section Styles -->
<style>
/* Newsletter Section Styles */
.newsletter-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 50%, var(--primary-color) 100%);
    color: var(--text-light);
    padding: var(--spacing-5xl) 0;
    position: relative;
    overflow: hidden;
}

/* Background Elements */
.newsletter-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.bg-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.bg-circle-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 15%;
    animation-delay: 2s;
}

.bg-circle-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Newsletter Content */
.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
    position: relative;
    z-index: 2;
}

/* Newsletter Main */
.newsletter-main {
    max-width: 500px;
}

.newsletter-header {
    margin-bottom: var(--spacing-2xl);
}

.newsletter-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-3xl);
    margin: 0 auto var(--spacing-lg);
    backdrop-filter: blur(10px);
}

.newsletter-title {
    font-size: var(--text-3xl);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-bold);
}

.newsletter-subtitle {
    font-size: var(--text-base);
    opacity: 0.9;
    line-height: var(--leading-relaxed);
}

/* Newsletter Form */
.newsletter-form {
    margin-bottom: var(--spacing-xl);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.input-container {
    position: relative;
    flex: 1;
}

.newsletter-input {
    width: 100%;
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-lg) 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    font-size: var(--text-base);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
}

.input-icon {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--text-lg);
}

.btn-newsletter {
    background: var(--text-light);
    color: var(--primary-color);
    border: none;
    padding: var(--spacing-lg) var(--spacing-2xl);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-newsletter:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-newsletter:active {
    transform: translateY(0);
}

.btn-newsletter .btn-text,
.btn-newsletter .btn-loading,
.btn-newsletter .btn-success {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Privacy Notice */
.privacy-notice {
    margin-top: var(--spacing-md);
}

.privacy-checkbox {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
}

.privacy-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: var(--radius-sm);
    background: transparent;
    position: relative;
    flex-shrink: 0;
    transition: all var(--transition-normal);
}

.privacy-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--text-light);
    border-color: var(--text-light);
}

.privacy-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-color);
    font-weight: var(--font-bold);
    font-size: var(--text-sm);
}

.privacy-text {
    opacity: 0.9;
}

.privacy-text a {
    color: var(--text-light);
    text-decoration: underline;
    font-weight: var(--font-medium);
}

.privacy-text a:hover {
    opacity: 0.8;
}

/* Success Message */
.newsletter-success {
    text-align: center;
    padding: var(--spacing-2xl);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.success-icon {
    font-size: var(--text-4xl);
    color: var(--success-color);
    margin-bottom: var(--spacing-lg);
}

.success-title {
    font-size: var(--text-xl);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-semibold);
}

.success-message {
    font-size: var(--text-sm);
    opacity: 0.9;
    line-height: var(--leading-relaxed);
}

/* Newsletter Benefits */
.newsletter-benefits {
    max-width: 500px;
}

.benefits-title {
    font-size: var(--text-2xl);
    margin-bottom: var(--spacing-xl);
    font-weight: var(--font-semibold);
}

.benefits-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.benefit-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(-4px);
}

.benefit-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    flex-shrink: 0;
}

.benefit-content {
    flex: 1;
}

.benefit-title {
    font-size: var(--text-base);
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-semibold);
}

.benefit-description {
    font-size: var(--text-sm);
    opacity: 0.9;
    line-height: var(--leading-normal);
}

/* Newsletter Statistics */
.newsletter-stats {
    margin-top: var(--spacing-4xl);
    position: relative;
    z-index: 2;
}

.stats-container {
    display: flex;
    justify-content: center;
    gap: var(--spacing-3xl);
    padding: var(--spacing-2xl);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--text-sm);
    opacity: 0.9;
}

/* Social Proof */
.social-proof {
    margin-top: var(--spacing-2xl);
    position: relative;
    z-index: 2;
}

.social-proof-text {
    font-size: var(--text-base);
    opacity: 0.9;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.social-proof-text i {
    font-size: var(--text-lg);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .newsletter-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-3xl);
        text-align: center;
    }
    
    .stats-container {
        gap: var(--spacing-2xl);
    }
}

@media (max-width: 768px) {
    .newsletter-section {
        padding: var(--spacing-4xl) 0;
    }
    
    .newsletter-title {
        font-size: var(--text-2xl);
    }
    
    .form-group {
        flex-direction: column;
    }
    
    .stats-container {
        flex-direction: column;
        gap: var(--spacing-lg);
    }
    
    .social-proof-text {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .newsletter-input {
        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) 45px;
    }
    
    .btn-newsletter {
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 48px;
    }
    
    .benefit-item {
        flex-direction: column;
        text-align: center;
    }
    
    .bg-circle {
        display: none;
    }
}
</style>

<!-- Newsletter JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const newsletterForm = document.getElementById('newsletterForm');
    const newsletterSuccess = document.getElementById('newsletterSuccess');
    
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('newsletterEmail').value;
            const privacyConsent = document.getElementById('privacyConsent').checked;
            const button = this.querySelector('.btn-newsletter');
            
            // Validate email
            if (!isValidEmail(email)) {
                if (window.toastManager) {
                    window.toastManager.show('يرجى إدخال بريد إلكتروني صحيح', 'error');
                }
                return;
            }
            
            // Check privacy consent
            if (!privacyConsent) {
                if (window.toastManager) {
                    window.toastManager.show('يرجى الموافقة على سياسة الخصوصية', 'warning');
                }
                return;
            }
            
            // Show loading state
            button.querySelector('.btn-text').style.display = 'none';
            button.querySelector('.btn-loading').style.display = 'flex';
            button.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                // Show success state
                button.querySelector('.btn-loading').style.display = 'none';
                button.querySelector('.btn-success').style.display = 'flex';
                button.style.backgroundColor = 'var(--success-color)';
                button.style.color = 'var(--text-light)';
                
                // Show success message
                newsletterForm.style.display = 'none';
                newsletterSuccess.style.display = 'block';
                
                // Show toast notification
                if (window.toastManager) {
                    window.toastManager.show('تم الاشتراك في النشرة الإخبارية بنجاح!', 'success');
                }
                
                // Add celebration effect
                createCelebrationEffect();
                
            }, 2000);
        });
    }
    
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function createCelebrationEffect() {
        const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];
        
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.style.cssText = `
                    position: fixed;
                    width: 10px;
                    height: 10px;
                    background: ${colors[Math.floor(Math.random() * colors.length)]};
                    top: -10px;
                    left: ${Math.random() * 100}vw;
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 9999;
                `;
                
                document.body.appendChild(confetti);
                
                confetti.animate([
                    { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
                    { transform: `translateY(100vh) rotate(720deg)`, opacity: 0 }
                ], {
                    duration: 3000 + Math.random() * 2000,
                    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
                }).onfinish = () => {
                    if (confetti.parentNode) {
                        confetti.parentNode.removeChild(confetti);
                    }
                };
            }, i * 100);
        }
    }
    
    // Add floating animation to benefit items
    const benefitItems = document.querySelectorAll('.benefit-item');
    benefitItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.2}s`;
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(-8px) scale(1.02)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0) scale(1)';
        });
    });
});
</script>
