<?php
/**
 * Professional Customer Reviews Section Component
 * Modern Arabic E-commerce Customer Reviews Display
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description Professional customer reviews section with database integration
 */

// Fetch customer reviews from database
try {
    $customerReviews = fetchAll("
        SELECT r.*, p.name as product_name, p.image as product_image
        FROM reviews r
        LEFT JOIN products p ON r.product_id = p.id
        WHERE r.status = 'approved' AND r.rating >= 4
        ORDER BY r.created_at DESC
        LIMIT 12
    ") ?: [];
} catch (Exception $e) {
    error_log("Error fetching customer reviews: " . $e->getMessage());
    $customerReviews = [];
}

// Function to format review date
function formatReviewDate($date) {
    $reviewDate = new DateTime($date);
    $now = new DateTime();
    $diff = $now->diff($reviewDate);
    
    if ($diff->days == 0) {
        return 'اليوم';
    } elseif ($diff->days == 1) {
        return 'أمس';
    } elseif ($diff->days < 7) {
        return 'منذ ' . $diff->days . ' أيام';
    } elseif ($diff->days < 30) {
        $weeks = floor($diff->days / 7);
        return 'منذ ' . $weeks . ' ' . ($weeks == 1 ? 'أسبوع' : 'أسابيع');
    } else {
        return $reviewDate->format('d/m/Y');
    }
}

// Function to generate star rating HTML
function generateStarRating($rating, $maxRating = 5) {
    $html = '';
    for ($i = 1; $i <= $maxRating; $i++) {
        if ($i <= $rating) {
            $html .= '<i class="star filled bi bi-star-fill"></i>';
        } else {
            $html .= '<i class="star empty bi bi-star"></i>';
        }
    }
    return $html;
}
?>

<!-- Customer Reviews Section - Professional Design -->
<section class="section reviews-section" id="customer-reviews">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header text-center">
            <span class="section-label">تقييمات العملاء</span>
            <h2 class="section-title">ماذا يقول عملاؤنا الكرام</h2>
            <p class="section-subtitle">اكتشف تجارب عملائنا الحقيقية مع منتجاتنا وخدماتنا المتميزة</p>
        </div>

        <?php if (!empty($customerReviews)): ?>
        <!-- Reviews Grid -->
        <div class="reviews-grid">
            <?php foreach (array_slice($customerReviews, 0, 6) as $index => $review): ?>
            <div class="review-card fade-in-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                <!-- Review Header -->
                <div class="review-header">
                    <div class="customer-info">
                        <div class="customer-avatar">
                            <?php 
                            $initials = mb_substr($review['customer_name'], 0, 1, 'UTF-8');
                            echo htmlspecialchars($initials);
                            ?>
                        </div>
                        <div class="customer-details">
                            <h4 class="customer-name"><?php echo htmlspecialchars($review['customer_name']); ?></h4>
                            <div class="review-date"><?php echo formatReviewDate($review['created_at']); ?></div>
                        </div>
                    </div>
                    
                    <!-- Star Rating -->
                    <div class="review-rating">
                        <?php echo generateStarRating($review['rating']); ?>
                        <span class="rating-number"><?php echo $review['rating']; ?>/5</span>
                    </div>
                </div>

                <!-- Review Content -->
                <div class="review-content">
                    <p class="review-text">"<?php echo htmlspecialchars($review['comment']); ?>"</p>
                    
                    <?php if (!empty($review['product_name'])): ?>
                    <div class="reviewed-product">
                        <?php if (!empty($review['product_image'])): ?>
                        <img src="<?php echo htmlspecialchars($review['product_image']); ?>" 
                             alt="<?php echo htmlspecialchars($review['product_name']); ?>"
                             class="product-thumb"
                             loading="lazy">
                        <?php endif; ?>
                        <div class="product-info">
                            <span class="product-label">المنتج:</span>
                            <span class="product-name"><?php echo htmlspecialchars($review['product_name']); ?></span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Review Footer -->
                <div class="review-footer">
                    <div class="review-actions">
                        <button class="btn-helpful" data-review-id="<?php echo $review['id']; ?>">
                            <i class="bi bi-hand-thumbs-up"></i>
                            <span>مفيد</span>
                            <span class="helpful-count"><?php echo $review['helpful_count'] ?? 0; ?></span>
                        </button>
                        
                        <button class="btn-share" data-review-id="<?php echo $review['id']; ?>">
                            <i class="bi bi-share"></i>
                            <span>مشاركة</span>
                        </button>
                    </div>
                    
                    <?php if ($review['is_verified'] ?? false): ?>
                    <div class="verified-badge">
                        <i class="bi bi-patch-check-fill"></i>
                        <span>عميل موثق</span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Show More Reviews Button -->
        <?php if (count($customerReviews) > 6): ?>
        <div class="reviews-actions text-center">
            <button class="btn btn-outline btn-lg" id="loadMoreReviews">
                <i class="bi bi-arrow-down-circle"></i>
                <span>عرض المزيد من التقييمات</span>
            </button>
        </div>
        <?php endif; ?>

        <!-- Reviews Statistics -->
        <div class="reviews-stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?php echo count($customerReviews); ?>+</div>
                    <div class="stat-label">تقييم إيجابي</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">4.8</div>
                    <div class="stat-label">متوسط التقييم</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">رضا العملاء</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">دعم العملاء</div>
                </div>
            </div>
        </div>

        <?php else: ?>
        <!-- No Reviews State -->
        <div class="no-reviews text-center">
            <div class="no-reviews-icon">
                <i class="bi bi-chat-quote" aria-hidden="true"></i>
            </div>
            <h3 class="no-reviews-title">لا توجد تقييمات حالياً</h3>
            <p class="no-reviews-text">كن أول من يشارك تجربته معنا ويساعد العملاء الآخرين</p>
            <a href="products.php" class="btn btn-primary btn-lg">
                <i class="bi bi-shop"></i>
                <span>تصفح المنتجات</span>
            </a>
        </div>
        <?php endif; ?>

        <!-- Call to Action -->
        <div class="reviews-cta text-center">
            <h3 class="cta-title">شارك تجربتك معنا</h3>
            <p class="cta-description">رأيك يهمنا ويساعد العملاء الآخرين في اتخاذ القرار الصحيح</p>
            <a href="reviews.php" class="btn btn-primary btn-lg">
                <i class="bi bi-star"></i>
                <span>اكتب تقييماً</span>
            </a>
        </div>
    </div>
</section>

<!-- Reviews Section Styles -->
<style>
/* Customer Reviews Section Styles */
.reviews-section {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    padding: var(--spacing-5xl) 0;
    position: relative;
    overflow: hidden;
}

.reviews-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.reviews-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-4xl);
}

/* Review Card Styles */
.review-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.review-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    right: var(--spacing-lg);
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.1;
    font-family: serif;
    line-height: 1;
}

.review-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Review Header */
.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.customer-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.customer-avatar {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    color: var(--text-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    flex-shrink: 0;
}

.customer-details {
    flex: 1;
}

.customer-name {
    font-size: var(--text-lg);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-semibold);
}

.review-date {
    font-size: var(--text-sm);
    color: var(--text-muted);
}

/* Star Rating */
.review-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex-shrink: 0;
}

.star {
    color: var(--warning-color);
    font-size: var(--text-sm);
}

.star.empty {
    color: var(--text-muted);
    opacity: 0.3;
}

.rating-number {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    margin-right: var(--spacing-xs);
}

/* Review Content */
.review-content {
    margin-bottom: var(--spacing-lg);
}

.review-text {
    font-size: var(--text-base);
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--spacing-md);
    font-style: italic;
}

/* Reviewed Product */
.reviewed-product {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background-color: var(--bg-light);
    border-radius: var(--radius-sm);
    margin-top: var(--spacing-md);
}

.product-thumb {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: var(--radius-sm);
    flex-shrink: 0;
}

.product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.product-label {
    font-size: var(--text-xs);
    color: var(--text-muted);
    font-weight: var(--font-medium);
}

.product-name {
    font-size: var(--text-sm);
    color: var(--text-primary);
    font-weight: var(--font-medium);
}

/* Review Footer */
.review-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.review-actions {
    display: flex;
    gap: var(--spacing-md);
}

.btn-helpful,
.btn-share {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: var(--text-sm);
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal);
}

.btn-helpful:hover,
.btn-share:hover {
    color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.1);
}

.helpful-count {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
}

.verified-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--success-color);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
}

/* Reviews Statistics */
.reviews-stats {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    margin-top: var(--spacing-4xl);
    box-shadow: var(--shadow-md);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
}

/* No Reviews State */
.no-reviews {
    padding: var(--spacing-5xl) 0;
}

.no-reviews-icon {
    font-size: var(--text-6xl);
    color: var(--text-muted);
    margin-bottom: var(--spacing-xl);
    opacity: 0.5;
}

.no-reviews-title {
    font-size: var(--text-2xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.no-reviews-text {
    font-size: var(--text-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Reviews CTA */
.reviews-cta {
    background: var(--primary-gradient);
    color: var(--text-light);
    padding: var(--spacing-3xl);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-4xl);
}

.cta-title {
    font-size: var(--text-2xl);
    margin-bottom: var(--spacing-md);
}

.cta-description {
    font-size: var(--text-base);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

.reviews-cta .btn {
    background-color: var(--text-light);
    color: var(--primary-color);
}

.reviews-cta .btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .reviews-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .review-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }
    
    .review-footer {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .customer-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .customer-avatar {
        width: 40px;
        height: 40px;
        font-size: var(--text-base);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- Reviews JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load more reviews functionality
    const loadMoreBtn = document.getElementById('loadMoreReviews');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            // Implement load more reviews functionality
            this.innerHTML = '<i class="bi bi-arrow-repeat"></i> <span>جاري التحميل...</span>';
            this.disabled = true;
            
            // Simulate loading
            setTimeout(() => {
                this.innerHTML = '<i class="bi bi-check"></i> <span>تم تحميل المزيد</span>';
                setTimeout(() => {
                    this.style.display = 'none';
                }, 1000);
            }, 1500);
        });
    }
    
    // Helpful button functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-helpful')) {
            const btn = e.target.closest('.btn-helpful');
            const countSpan = btn.querySelector('.helpful-count');
            let count = parseInt(countSpan.textContent) || 0;
            
            btn.style.color = 'var(--success-color)';
            countSpan.textContent = count + 1;
            btn.disabled = true;
            
            // Show toast notification
            if (window.toastManager) {
                window.toastManager.show('شكراً لك! تم تسجيل رأيك', 'success');
            }
        }
    });
    
    // Share button functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-share')) {
            if (navigator.share) {
                navigator.share({
                    title: 'تقييم عميل - متجر Care',
                    text: 'شاهد هذا التقييم الرائع من أحد عملائنا',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                if (window.toastManager) {
                    window.toastManager.show('تم نسخ الرابط إلى الحافظة', 'info');
                }
            }
        }
    });
});
</script>
