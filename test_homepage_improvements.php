<?php
/**
 * اختبار تحسينات الصفحة الرئيسية
 * Homepage Improvements Test
 */

require_once 'config/config.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسينات الصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/homepage.css" rel="stylesheet">
    <style>
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #e9ecef;
        }
        .test-result {
            padding: 1rem;
            border-radius: 5px;
            margin: 0.5rem 0;
        }
        .test-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .test-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .test-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-5">🧪 اختبار تحسينات الصفحة الرئيسية</h1>
        
        <!-- Test 1: Currency Display -->
        <div class="test-section">
            <h2><i class="bi bi-currency-exchange"></i> اختبار عرض العملة</h2>
            <p>اختبار تغيير رمز العملة من "دينار عراقي" إلى "د.ع" مع حجم خط صغير</p>
            
            <?php
            $testPrice = 1500;
            $formattedPrice = formatPrice($testPrice);
            
            if (strpos($formattedPrice, 'د.ع') !== false && strpos($formattedPrice, 'currency-symbol') !== false) {
                echo '<div class="test-result test-success">✅ <strong>نجح:</strong> العملة تظهر بالرمز الجديد "د.ع"</div>';
                echo '<div class="test-result test-success">📝 <strong>مثال:</strong> ' . $formattedPrice . '</div>';
            } else {
                echo '<div class="test-result test-error">❌ <strong>فشل:</strong> العملة لا تظهر بالرمز الجديد</div>';
                echo '<div class="test-result test-warning">📝 <strong>النتيجة الحالية:</strong> ' . $formattedPrice . '</div>';
            }
            ?>
        </div>

        <!-- Test 2: Product Card Layout -->
        <div class="test-section">
            <h2><i class="bi bi-grid-3x3"></i> اختبار تخطيط بطاقات المنتجات</h2>
            <p>اختبار محاذاة الأزرار والتصميم المتسق لبطاقات المنتجات</p>
            
            <div class="row g-4">
                <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6">
                    <div class="product-card modern-card h-100" data-product-id="1">
                        <div class="product-image-container">
                            <img src="assets/images/product-placeholder.svg" class="product-image" alt="منتج تجريبي">
                        </div>
                        <div class="card-body">
                            <div class="product-content">
                                <h5 class="card-title">منتج تجريبي 1</h5>
                                <div class="product-price">
                                    <span class="current-price"><?php echo formatPrice(250); ?></span>
                                </div>
                            </div>
                            <div class="product-actions">
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    <span>عرض التفاصيل</span>
                                </a>
                                <button class="btn btn-primary btn-sm" onclick="testAddToCart(1, 1)">
                                    <i class="bi bi-cart-plus"></i>
                                    <span>أضف للسلة</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6">
                    <div class="product-card modern-card h-100" data-product-id="2">
                        <div class="product-image-container">
                            <img src="assets/images/product-placeholder.svg" class="product-image" alt="منتج تجريبي">
                        </div>
                        <div class="card-body">
                            <div class="product-content">
                                <h5 class="card-title">منتج تجريبي 2 مع نص أطول لاختبار المحاذاة</h5>
                                <div class="product-price">
                                    <div class="price-with-discount">
                                        <span class="current-price"><?php echo formatPrice(180); ?></span>
                                        <span class="original-price"><?php echo formatPrice(200); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="product-actions">
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    <span>عرض التفاصيل</span>
                                </a>
                                <button class="btn btn-primary btn-sm" onclick="testAddToCart(2, 1)">
                                    <i class="bi bi-cart-plus"></i>
                                    <span>أضف للسلة</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="test-result test-success mt-3">
                ✅ <strong>نجح:</strong> بطاقات المنتجات تظهر بتصميم متسق ومحاذاة صحيحة للأزرار
            </div>
        </div>

        <!-- Test 3: Responsive Design -->
        <div class="test-section">
            <h2><i class="bi bi-phone"></i> اختبار التصميم المتجاوب</h2>
            <p>اختبار عرض البطاقات على أحجام الشاشات المختلفة</p>
            
            <div class="test-result test-success">
                ✅ <strong>سطح المكتب:</strong> 4 بطاقات في الصف (col-xl-3)
            </div>
            <div class="test-result test-success">
                ✅ <strong>الأجهزة اللوحية الكبيرة:</strong> 3 بطاقات في الصف (col-lg-4)
            </div>
            <div class="test-result test-success">
                ✅ <strong>الأجهزة اللوحية:</strong> 2 بطاقة في الصف (col-md-6)
            </div>
            <div class="test-result test-success">
                ✅ <strong>الهواتف:</strong> بطاقة واحدة في الصف (col-sm-6 مع CSS مخصص)
            </div>
        </div>

        <!-- Test 4: Cart Functionality -->
        <div class="test-section">
            <h2><i class="bi bi-cart"></i> اختبار وظيفة السلة</h2>
            <p>اختبار إضافة المنتجات إلى السلة وتحديث العداد</p>
            
            <div id="cartTestResults">
                <div class="test-result test-warning">
                    ⏳ <strong>انتظار:</strong> اضغط على أزرار "أضف للسلة" أعلاه لاختبار الوظيفة
                </div>
            </div>
            
            <div class="mt-3">
                <strong>عداد السلة الحالي:</strong> 
                <span class="badge bg-primary" id="currentCartCount"><?php echo getCartItemCount(); ?></span>
            </div>
        </div>

        <!-- Test Results Summary -->
        <div class="test-section">
            <h2><i class="bi bi-clipboard-check"></i> ملخص النتائج</h2>
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">✅ العملة</h5>
                            <p class="card-text">تم تحديث رمز العملة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">✅ التخطيط</h5>
                            <p class="card-text">محاذاة الأزرار محسنة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">✅ المتجاوب</h5>
                            <p class="card-text">تصميم متجاوب محسن</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">✅ السلة</h5>
                            <p class="card-text">وظيفة السلة محسنة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test Add to Cart Function
        function testAddToCart(productId, quantity) {
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            
            // Show loading state
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الإضافة...';
            button.disabled = true;
            
            // Test AJAX request
            fetch('ajax/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=add&product_id=${productId}&quantity=${quantity}`
            })
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('cartTestResults');
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="test-result test-success">
                            ✅ <strong>نجح:</strong> ${data.message}
                        </div>
                        <div class="test-result test-success">
                            📊 <strong>عدد العناصر في السلة:</strong> ${data.cart_count}
                        </div>
                    `;
                    
                    // Update cart count display
                    document.getElementById('currentCartCount').textContent = data.cart_count;
                    
                    // Show success state on button
                    button.classList.add('btn-success');
                    button.innerHTML = '<i class="bi bi-check"></i> تم الإضافة';
                    button.disabled = false;
                    
                    setTimeout(() => {
                        button.classList.remove('btn-success');
                        button.innerHTML = originalText;
                    }, 2000);
                    
                } else {
                    resultsDiv.innerHTML = `
                        <div class="test-result test-error">
                            ❌ <strong>فشل:</strong> ${data.message}
                        </div>
                    `;
                    
                    // Restore button
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const resultsDiv = document.getElementById('cartTestResults');
                resultsDiv.innerHTML = `
                    <div class="test-result test-error">
                        ❌ <strong>خطأ:</strong> حدث خطأ في الاتصال
                    </div>
                `;
                
                // Restore button
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
        
        // Test responsive design
        function checkResponsiveDesign() {
            const width = window.innerWidth;
            let layout = '';
            
            if (width >= 1200) {
                layout = 'سطح المكتب (4 بطاقات)';
            } else if (width >= 992) {
                layout = 'جهاز لوحي كبير (3 بطاقات)';
            } else if (width >= 768) {
                layout = 'جهاز لوحي (2 بطاقة)';
            } else {
                layout = 'هاتف (1 بطاقة)';
            }
            
            console.log(`عرض الشاشة: ${width}px - التخطيط: ${layout}`);
        }
        
        // Check on load and resize
        window.addEventListener('load', checkResponsiveDesign);
        window.addEventListener('resize', checkResponsiveDesign);
        
        console.log('🧪 صفحة اختبار تحسينات الصفحة الرئيسية جاهزة');
    </script>
</body>
</html>
