<?php
/**
 * Professional Social Media Section Component
 * Modern Arabic E-commerce Social Media Links
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description Professional social media section with interactive links
 */

// Social media platforms data
$socialPlatforms = [
    [
        'name' => 'فيسبوك',
        'icon' => 'bi-facebook',
        'url' => 'https://facebook.com/carestore',
        'color' => '#1877F2',
        'followers' => '25K',
        'description' => 'تابعنا للحصول على آخر الأخبار والعروض'
    ],
    [
        'name' => 'إنستغرام',
        'icon' => 'bi-instagram',
        'url' => 'https://instagram.com/carestore',
        'color' => '#E4405F',
        'followers' => '18K',
        'description' => 'شاهد صور منتجاتنا ونصائح الجمال اليومية'
    ],
    [
        'name' => 'تيك توك',
        'icon' => 'bi-tiktok',
        'url' => 'https://tiktok.com/@carestore',
        'color' => '#000000',
        'followers' => '12K',
        'description' => 'فيديوهات ممتعة ونصائح سريعة للعناية'
    ],
    [
        'name' => 'يوتيوب',
        'icon' => 'bi-youtube',
        'url' => 'https://youtube.com/carestore',
        'color' => '#FF0000',
        'followers' => '8K',
        'description' => 'شروحات مفصلة وتجارب المنتجات'
    ],
    [
        'name' => 'تويتر',
        'icon' => 'bi-twitter',
        'url' => 'https://twitter.com/carestore',
        'color' => '#1DA1F2',
        'followers' => '5K',
        'description' => 'آخر الأخبار والتحديثات السريعة'
    ],
    [
        'name' => 'واتساب',
        'icon' => 'bi-whatsapp',
        'url' => 'https://wa.me/9647801234567',
        'color' => '#25D366',
        'followers' => 'دردشة',
        'description' => 'تواصل معنا مباشرة للاستفسارات'
    ]
];

// Social media statistics
$socialStats = [
    [
        'number' => '68K+',
        'label' => 'إجمالي المتابعين',
        'icon' => 'bi-people-fill'
    ],
    [
        'number' => '1.2M+',
        'label' => 'مشاهدات شهرياً',
        'icon' => 'bi-eye-fill'
    ],
    [
        'number' => '95%',
        'label' => 'معدل التفاعل',
        'icon' => 'bi-heart-fill'
    ]
];
?>

<!-- Social Media Section - Professional Design -->
<section class="social-section" id="social-media">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header text-center">
            <span class="section-label">تواصل معنا</span>
            <h2 class="section-title">تابعنا على مواقع التواصل الاجتماعي</h2>
            <p class="section-subtitle">
                انضم إلى مجتمعنا المتنامي واحصل على نصائح الجمال والعناية اليومية، 
                آخر العروض، وكن أول من يعرف عن المنتجات الجديدة
            </p>
        </div>

        <!-- Social Media Links -->
        <div class="social-platforms">
            <?php foreach ($socialPlatforms as $index => $platform): ?>
            <a href="<?php echo htmlspecialchars($platform['url']); ?>" 
               class="social-platform fade-in-up" 
               style="animation-delay: <?php echo $index * 0.1; ?>s;"
               target="_blank" 
               rel="noopener noreferrer"
               data-platform="<?php echo strtolower($platform['name']); ?>">
               
                <div class="platform-icon" style="--platform-color: <?php echo $platform['color']; ?>">
                    <i class="<?php echo $platform['icon']; ?>"></i>
                </div>
                
                <div class="platform-content">
                    <div class="platform-header">
                        <h3 class="platform-name"><?php echo htmlspecialchars($platform['name']); ?></h3>
                        <div class="platform-followers"><?php echo htmlspecialchars($platform['followers']); ?></div>
                    </div>
                    <p class="platform-description"><?php echo htmlspecialchars($platform['description']); ?></p>
                </div>
                
                <div class="platform-arrow">
                    <i class="bi bi-arrow-left"></i>
                </div>
                
                <div class="platform-hover-effect"></div>
            </a>
            <?php endforeach; ?>
        </div>

        <!-- Social Media Statistics -->
        <div class="social-stats">
            <div class="stats-header text-center">
                <h3 class="stats-title">إحصائيات مواقع التواصل</h3>
                <p class="stats-subtitle">أرقام تعكس ثقة متابعينا وتفاعلهم معنا</p>
            </div>
            
            <div class="stats-grid">
                <?php foreach ($socialStats as $index => $stat): ?>
                <div class="social-stat-card fade-in-up" style="animation-delay: <?php echo ($index + 6) * 0.1; ?>s;">
                    <div class="stat-icon">
                        <i class="<?php echo $stat['icon']; ?>"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo htmlspecialchars($stat['number']); ?></div>
                        <div class="stat-label"><?php echo htmlspecialchars($stat['label']); ?></div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Social Media Feed Preview -->
        <div class="social-feed-preview">
            <div class="feed-header text-center">
                <h3 class="feed-title">آخر منشوراتنا</h3>
                <p class="feed-subtitle">شاهد أحدث المحتوى من حساباتنا الرسمية</p>
            </div>
            
            <div class="feed-grid">
                <!-- Instagram-style posts -->
                <div class="feed-post instagram-post fade-in-up">
                    <div class="post-header">
                        <div class="post-platform">
                            <i class="bi bi-instagram"></i>
                            <span>إنستغرام</span>
                        </div>
                        <div class="post-time">منذ ساعتين</div>
                    </div>
                    <div class="post-image">
                        <img src="assets/images/social-post-1.jpg" alt="منشور إنستغرام" loading="lazy">
                        <div class="post-overlay">
                            <div class="post-stats">
                                <span><i class="bi bi-heart-fill"></i> 245</span>
                                <span><i class="bi bi-chat-fill"></i> 18</span>
                            </div>
                        </div>
                    </div>
                    <div class="post-content">
                        <p>نصائح للعناية بالبشرة في فصل الشتاء ❄️✨</p>
                    </div>
                </div>
                
                <div class="feed-post facebook-post fade-in-up" style="animation-delay: 0.1s;">
                    <div class="post-header">
                        <div class="post-platform">
                            <i class="bi bi-facebook"></i>
                            <span>فيسبوك</span>
                        </div>
                        <div class="post-time">منذ 4 ساعات</div>
                    </div>
                    <div class="post-content">
                        <p>🎉 عرض خاص! خصم 30% على جميع منتجات العناية بالشعر</p>
                        <div class="post-cta">
                            <span class="cta-text">تسوق الآن</span>
                            <i class="bi bi-arrow-left"></i>
                        </div>
                    </div>
                </div>
                
                <div class="feed-post youtube-post fade-in-up" style="animation-delay: 0.2s;">
                    <div class="post-header">
                        <div class="post-platform">
                            <i class="bi bi-youtube"></i>
                            <span>يوتيوب</span>
                        </div>
                        <div class="post-time">منذ يوم</div>
                    </div>
                    <div class="post-video">
                        <div class="video-thumbnail">
                            <img src="assets/images/video-thumb-1.jpg" alt="فيديو يوتيوب" loading="lazy">
                            <div class="play-button">
                                <i class="bi bi-play-fill"></i>
                            </div>
                        </div>
                    </div>
                    <div class="post-content">
                        <p>روتين العناية اليومي للبشرة المختلطة</p>
                        <div class="video-stats">
                            <span><i class="bi bi-eye-fill"></i> 1.2K مشاهدة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Media CTA -->
        <div class="social-cta text-center">
            <h3 class="cta-title">لا تفوت أي جديد!</h3>
            <p class="cta-description">
                تابعنا على جميع منصات التواصل الاجتماعي للحصول على المحتوى الحصري والعروض الخاصة
            </p>
            <div class="cta-buttons">
                <button class="btn btn-primary btn-lg" id="followAllBtn">
                    <i class="bi bi-plus-circle"></i>
                    <span>متابعة جميع الحسابات</span>
                </button>
                <button class="btn btn-outline btn-lg" id="sharePageBtn">
                    <i class="bi bi-share"></i>
                    <span>مشاركة الصفحة</span>
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Social Media Section Styles -->
<style>
/* Social Media Section Styles */
.social-section {
    background-color: var(--bg-secondary);
    padding: var(--spacing-5xl) 0;
    position: relative;
}

/* Social Platforms */
.social-platforms {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-5xl);
}

.social-platform {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.social-platform:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
    color: var(--text-primary);
}

.platform-hover-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.social-platform:hover .platform-hover-effect {
    left: 100%;
}

/* Platform Icon */
.platform-icon {
    width: 60px;
    height: 60px;
    background-color: var(--platform-color);
    color: var(--text-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    flex-shrink: 0;
    transition: all var(--transition-normal);
}

.social-platform:hover .platform-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Platform Content */
.platform-content {
    flex: 1;
}

.platform-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.platform-name {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    margin: 0;
}

.platform-followers {
    font-size: var(--text-sm);
    color: var(--text-muted);
    background-color: var(--bg-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: var(--font-medium);
}

.platform-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-normal);
    margin: 0;
}

/* Platform Arrow */
.platform-arrow {
    color: var(--text-muted);
    font-size: var(--text-lg);
    transition: all var(--transition-normal);
}

.social-platform:hover .platform-arrow {
    color: var(--platform-color);
    transform: translateX(-4px);
}

/* Social Statistics */
.social-stats {
    margin-bottom: var(--spacing-5xl);
}

.stats-header {
    margin-bottom: var(--spacing-3xl);
}

.stats-title {
    font-size: var(--text-2xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-semibold);
}

.stats-subtitle {
    font-size: var(--text-base);
    color: var(--text-secondary);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
}

.social-stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-2xl);
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.social-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.social-stat-card .stat-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    color: var(--text-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.social-stat-card .stat-number {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.social-stat-card .stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
}

/* Social Feed Preview */
.social-feed-preview {
    margin-bottom: var(--spacing-5xl);
}

.feed-header {
    margin-bottom: var(--spacing-3xl);
}

.feed-title {
    font-size: var(--text-2xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-semibold);
}

.feed-subtitle {
    font-size: var(--text-base);
    color: var(--text-secondary);
}

.feed-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

/* Feed Post Styles */
.feed-post {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.feed-post:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.post-platform {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
}

.post-time {
    font-size: var(--text-xs);
    color: var(--text-muted);
}

.post-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.feed-post:hover .post-image img {
    transform: scale(1.05);
}

.post-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.feed-post:hover .post-overlay {
    opacity: 1;
}

.post-stats {
    display: flex;
    gap: var(--spacing-lg);
    color: var(--text-light);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
}

.post-content {
    padding: var(--spacing-lg);
}

.post-content p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--spacing-md);
}

.post-cta {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary-color);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.post-cta:hover {
    gap: var(--spacing-sm);
}

/* Video Post Styles */
.post-video {
    position: relative;
    height: 180px;
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 100%;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 0, 0, 0.9);
    color: var(--text-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.play-button:hover {
    background: #ff0000;
    transform: translate(-50%, -50%) scale(1.1);
}

.video-stats {
    font-size: var(--text-xs);
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Social CTA */
.social-cta {
    background: var(--primary-gradient);
    color: var(--text-light);
    padding: var(--spacing-4xl);
    border-radius: var(--radius-lg);
}

.cta-title {
    font-size: var(--text-3xl);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-bold);
}

.cta-description {
    font-size: var(--text-lg);
    margin-bottom: var(--spacing-2xl);
    opacity: 0.9;
    line-height: var(--leading-relaxed);
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.social-cta .btn {
    background-color: var(--text-light);
    color: var(--primary-color);
    border-color: var(--text-light);
}

.social-cta .btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.social-cta .btn-outline {
    background-color: transparent;
    color: var(--text-light);
    border-color: var(--text-light);
}

.social-cta .btn-outline:hover {
    background-color: var(--text-light);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .social-platforms {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .feed-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .social-platform {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .platform-header {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .feed-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .social-section {
        padding: var(--spacing-4xl) 0;
    }
    
    .platform-icon {
        width: 50px;
        height: 50px;
        font-size: var(--text-xl);
    }
    
    .cta-title {
        font-size: var(--text-2xl);
    }
    
    .cta-description {
        font-size: var(--text-base);
    }
}
</style>

<!-- Social Media JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Follow all button functionality
    const followAllBtn = document.getElementById('followAllBtn');
    if (followAllBtn) {
        followAllBtn.addEventListener('click', function() {
            const socialLinks = document.querySelectorAll('.social-platform');
            let delay = 0;
            
            socialLinks.forEach(link => {
                setTimeout(() => {
                    window.open(link.href, '_blank');
                }, delay);
                delay += 500; // 500ms delay between each window
            });
            
            if (window.toastManager) {
                window.toastManager.show('تم فتح جميع حسابات التواصل الاجتماعي', 'success');
            }
        });
    }
    
    // Share page button functionality
    const sharePageBtn = document.getElementById('sharePageBtn');
    if (sharePageBtn) {
        sharePageBtn.addEventListener('click', function() {
            if (navigator.share) {
                navigator.share({
                    title: 'متجر Care للعناية والجمال',
                    text: 'اكتشف أفضل منتجات العناية والجمال في متجر Care',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    if (window.toastManager) {
                        window.toastManager.show('تم نسخ رابط الصفحة إلى الحافظة', 'success');
                    }
                });
            }
        });
    }
    
    // Track social platform clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('.social-platform')) {
            const platform = e.target.closest('.social-platform').dataset.platform;
            
            // Add click animation
            const platformIcon = e.target.closest('.social-platform').querySelector('.platform-icon');
            platformIcon.style.transform = 'scale(1.2) rotate(10deg)';
            
            setTimeout(() => {
                platformIcon.style.transform = '';
            }, 300);
            
            // Analytics tracking (if implemented)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'social_click', {
                    'platform': platform,
                    'event_category': 'social_media'
                });
            }
        }
    });
    
    // Add hover effects to feed posts
    const feedPosts = document.querySelectorAll('.feed-post');
    feedPosts.forEach(post => {
        post.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        post.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Play button click for video posts
    document.addEventListener('click', function(e) {
        if (e.target.closest('.play-button')) {
            e.preventDefault();
            const playButton = e.target.closest('.play-button');
            
            // Add click animation
            playButton.style.transform = 'translate(-50%, -50%) scale(1.3)';
            playButton.style.background = '#ff0000';
            
            setTimeout(() => {
                playButton.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 200);
            
            if (window.toastManager) {
                window.toastManager.show('سيتم توجيهك إلى الفيديو على يوتيوب', 'info');
            }
            
            // Simulate opening YouTube video
            setTimeout(() => {
                window.open('https://youtube.com/carestore', '_blank');
            }, 1000);
        }
    });
});
</script>
