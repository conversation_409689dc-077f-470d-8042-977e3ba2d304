<?php
/**
 * Professional Categories Section Component
 * Modern Arabic E-commerce Categories Display
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description Attractive category cards with icons and explore buttons
 */

// Fetch categories from database
$categories = [];
try {
    $categoriesQuery = "
        SELECT id, name, description, image, status, created_at
        FROM categories 
        WHERE status = 'active' 
        ORDER BY name ASC
        LIMIT 8
    ";
    $categories = fetchAll($categoriesQuery);
    
    if (!$categories) {
        $categories = [];
    }
} catch (Exception $e) {
    error_log("Error fetching categories: " . $e->getMessage());
    $categories = [];
}

// Default category icons mapping
$categoryIcons = [
    'إلكترونيات' => 'bi-laptop',
    'ملابس' => 'bi-bag',
    'منزل ومطبخ' => 'bi-house',
    'رياضة' => 'bi-trophy',
    'كتب' => 'bi-book',
    'عناية بالشعر' => 'bi-scissors',
    'عناية بالبشرة' => 'bi-heart',
    'مكياج' => 'bi-palette',
    'عطور' => 'bi-flower1',
    'صحة وجمال' => 'bi-heart-pulse',
    'أطفال' => 'bi-balloon',
    'إكسسوارات' => 'bi-gem'
];

// Function to get category icon
function getCategoryIcon($categoryName, $categoryIcons) {
    foreach ($categoryIcons as $key => $icon) {
        if (strpos($categoryName, $key) !== false) {
            return $icon;
        }
    }
    return 'bi-grid-3x3-gap'; // Default icon
}
?>

<!-- Categories Section - Professional Design -->
<section class="section categories-section" id="categories">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header">
            <h2 class="section-title">فئات المنتجات</h2>
            <p class="section-subtitle">اكتشف مجموعتنا المتنوعة من المنتجات عالية الجودة</p>
        </div>

        <?php if (!empty($categories)): ?>
        <!-- Categories Grid -->
        <div class="categories-grid grid grid-cols-4">
            <?php foreach ($categories as $index => $category): ?>
            <div class="category-card fade-in-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                <!-- Category Icon -->
                <div class="category-icon-container">
                    <i class="category-icon <?php echo getCategoryIcon($category['name'], $categoryIcons); ?>" aria-hidden="true"></i>
                </div>

                <!-- Category Content -->
                <div class="category-content">
                    <h3 class="category-title"><?php echo htmlspecialchars($category['name']); ?></h3>
                    
                    <?php if (!empty($category['description'])): ?>
                    <p class="category-description">
                        <?php echo htmlspecialchars($category['description']); ?>
                    </p>
                    <?php endif; ?>

                    <!-- Category Image (if available) -->
                    <?php if (!empty($category['image'])): ?>
                    <div class="category-image-container">
                        <img src="<?php echo htmlspecialchars($category['image']); ?>" 
                             alt="<?php echo htmlspecialchars($category['name']); ?>"
                             class="category-image"
                             loading="lazy">
                    </div>
                    <?php endif; ?>

                    <!-- Explore Button -->
                    <a href="products.php?category=<?php echo $category['id']; ?>" 
                       class="btn-explore"
                       role="button"
                       aria-label="استكشاف منتجات <?php echo htmlspecialchars($category['name']); ?>">
                        <i class="bi bi-arrow-left" aria-hidden="true"></i>
                        <span>استكشف</span>
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- View All Categories Button -->
        <div class="section-footer text-center mt-5">
            <a href="categories.php" class="btn btn-outline btn-lg">
                <i class="bi bi-grid-3x3-gap" aria-hidden="true"></i>
                <span>عرض جميع الفئات</span>
            </a>
        </div>

        <?php else: ?>
        <!-- No Categories Available -->
        <div class="no-categories text-center">
            <div class="no-categories-icon">
                <i class="bi bi-grid-3x3-gap" aria-hidden="true"></i>
            </div>
            <h3 class="no-categories-title">لا توجد فئات متاحة حالياً</h3>
            <p class="no-categories-text">سيتم إضافة الفئات قريباً. تابعونا للحصول على آخر التحديثات.</p>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Categories Section Styles -->
<style>
/* Categories Section Specific Styles */
.categories-section {
    background-color: var(--bg-primary);
    padding: var(--spacing-4xl) 0;
}

.categories-grid {
    gap: var(--spacing-xl);
}

.category-card {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.category-card:hover {
    background-color: var(--bg-primary);
    box-shadow: var(--shadow-lg);
    transform: translateY(-8px);
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.category-card:hover::before {
    transform: scaleX(1);
}

.category-icon-container {
    margin-bottom: var(--spacing-lg);
}

.category-icon {
    font-size: var(--text-5xl);
    color: var(--primary-color);
    transition: all var(--transition-normal);
}

.category-card:hover .category-icon {
    color: var(--primary-dark);
    transform: scale(1.1);
}

.category-content {
    position: relative;
    z-index: 2;
}

.category-title {
    font-size: var(--text-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-semibold);
}

.category-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-normal);
    margin-bottom: var(--spacing-lg);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.category-image-container {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    border-radius: 50%;
    overflow: hidden;
    background-color: var(--bg-secondary);
}

.category-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.category-card:hover .category-image {
    transform: scale(1.1);
}

.btn-explore {
    background-color: var(--secondary-color);
    color: var(--text-light);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-sm);
    text-decoration: none;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-explore:hover {
    background-color: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* No Categories State */
.no-categories {
    padding: var(--spacing-4xl) 0;
}

.no-categories-icon {
    font-size: var(--text-6xl);
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

.no-categories-title {
    font-size: var(--text-2xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.no-categories-text {
    font-size: var(--text-base);
    color: var(--text-secondary);
    max-width: 500px;
    margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .categories-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    .category-card {
        padding: var(--spacing-lg);
    }
    
    .category-icon {
        font-size: var(--text-4xl);
    }
}

@media (max-width: 480px) {
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .categories-section {
        padding: var(--spacing-3xl) 0;
    }
}
</style>
