<?php
/**
 * Comprehensive Homepage Professional Standards Test
 * Tests all aspects of the enhanced Arabic e-commerce homepage
 */

// Test configuration
$testResults = [];
$totalTests = 0;
$passedTests = 0;
$errors = [];
$warnings = [];
$recommendations = [];

function runTest($testName, $condition, $description = '', $type = 'info') {
    global $testResults, $totalTests, $passedTests, $errors, $warnings;
    $totalTests++;
    $result = $condition ? 'PASS' : 'FAIL';
    if ($condition) $passedTests++;
    
    $testResults[] = [
        'name' => $testName,
        'result' => $result,
        'description' => $description,
        'type' => $type
    ];
    
    if (!$condition) {
        if ($type === 'critical') {
            $errors[] = $testName . ': ' . $description;
        } else {
            $warnings[] = $testName . ': ' . $description;
        }
    }
    
    return $condition;
}

// Check file existence
$indexFile = 'index.php';
$cssFile = 'assets/css/homepage.css';
$headerFile = 'includes/header.php';
$footerFile = 'includes/footer.php';
$carouselFile = 'includes/homepage_carousel.php';

$filesExist = [
    'index.php' => file_exists($indexFile),
    'homepage.css' => file_exists($cssFile),
    'header.php' => file_exists($headerFile),
    'footer.php' => file_exists($footerFile),
    'carousel.php' => file_exists($carouselFile)
];

foreach ($filesExist as $file => $exists) {
    runTest("File: $file", $exists, "Required file exists", 'critical');
}

if ($filesExist['index.php']) {
    $indexContent = file_get_contents($indexFile);
    
    // Test 1: Technical Issues & PHP Errors
    runTest('PHP Error Handling', 
        strpos($indexContent, 'try {') !== false && strpos($indexContent, 'catch') !== false,
        'Proper PHP error handling implemented');
    
    runTest('Database Error Handling', 
        strpos($indexContent, 'error_log') !== false,
        'Database error logging implemented');
    
    runTest('Function Existence Check', 
        strpos($indexContent, 'function_exists') !== false,
        'Function existence checks implemented');
    
    runTest('Prepared Statements', 
        strpos($indexContent, 'fetchAll("') === false || strpos($indexContent, '?') !== false,
        'Prepared statements used for database queries');
    
    // Test 2: Design & Visual Standards
    runTest('Enhanced Influencers Section', 
        strpos($indexContent, 'influencer-card-enhanced') !== false,
        'Enhanced influencer cards with better contrast');
    
    runTest('Professional Success Story', 
        strpos($indexContent, 'success-story-section') !== false,
        'Professional success story section');
    
    runTest('Brand Timeline', 
        strpos($indexContent, 'brand-timeline-section') !== false,
        'Brand story timeline component');
    
    runTest('Consistent CSS Classes', 
        strpos($indexContent, 'homepage-section') !== false,
        'Consistent section styling');
    
    // Test 3: Arabic RTL & Typography
    runTest('RTL Direction', 
        strpos($indexContent, 'dir="rtl"') !== false,
        'Proper RTL direction set');
    
    runTest('Arabic Font Loading', 
        strpos($indexContent, 'Cairo') !== false,
        'Arabic fonts properly loaded');
    
    runTest('Iraqi Dinar Formatting', 
        strpos($indexContent, 'formatPrice') !== false || strpos($indexContent, 'د.ع') !== false,
        'Iraqi Dinar currency formatting');
    
    // Test 4: Content & User Experience
    runTest('Error Fallbacks', 
        strpos($indexContent, 'empty(') !== false,
        'Content fallbacks for empty data');
    
    runTest('Loading States', 
        strpos($indexContent, 'loading') !== false || strpos($indexContent, 'جاري') !== false,
        'Loading states implemented');
    
    runTest('User Feedback', 
        strpos($indexContent, 'showToast') !== false,
        'User feedback notifications');
    
    // Test 5: Performance & Accessibility
    runTest('Lazy Loading', 
        strpos($indexContent, 'data-src') !== false || strpos($indexContent, 'IntersectionObserver') !== false,
        'Image lazy loading implemented');
    
    runTest('Animation Optimization', 
        strpos($indexContent, 'fade-in') !== false,
        'Smooth animations implemented');
    
    runTest('Accessibility Features', 
        strpos($indexContent, 'alt=') !== false && strpos($indexContent, 'aria-') !== false,
        'Accessibility attributes present');
}

if ($filesExist['homepage.css']) {
    $cssContent = file_get_contents($cssFile);
    
    // Test CSS Optimizations
    runTest('CSS Variables', 
        strpos($cssContent, '--primary-color') !== false,
        'CSS custom properties for consistency');
    
    runTest('Responsive Design', 
        strpos($cssContent, '@media (max-width:') !== false,
        'Responsive breakpoints implemented');
    
    runTest('RTL Support', 
        strpos($cssContent, '[dir="rtl"]') !== false,
        'Comprehensive RTL support');
    
    runTest('Performance Optimizations', 
        strpos($cssContent, 'will-change') !== false || strpos($cssContent, 'transform3d') !== false,
        'CSS performance optimizations');
    
    runTest('Accessibility CSS', 
        strpos($cssContent, 'prefers-reduced-motion') !== false,
        'Accessibility CSS features');
    
    runTest('Print Styles', 
        strpos($cssContent, '@media print') !== false,
        'Print optimization styles');
    
    runTest('Dark Mode Support', 
        strpos($cssContent, 'prefers-color-scheme') !== false,
        'Dark mode support implemented');
    
    // Test Visual Consistency
    runTest('Color Consistency', 
        substr_count($cssContent, 'var(--') >= 10,
        'Consistent color usage with variables');
    
    runTest('Typography Scale', 
        strpos($cssContent, '--font-size-') !== false,
        'Consistent typography scale');
    
    runTest('Shadow System', 
        strpos($cssContent, '--shadow-') !== false,
        'Consistent shadow system');
}

// Calculate success rate
$successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 1) : 0;

// Determine overall status
$overallStatus = 'excellent';
if ($successRate < 70) $overallStatus = 'needs-improvement';
elseif ($successRate < 85) $overallStatus = 'good';
elseif ($successRate < 95) $overallStatus = 'very-good';

// Generate recommendations
if (count($errors) > 0) {
    $recommendations[] = "حل الأخطاء الحرجة: " . count($errors) . " خطأ حرج يحتاج إلى إصلاح فوري";
}
if (count($warnings) > 0) {
    $recommendations[] = "تحسين التحذيرات: " . count($warnings) . " تحذير يمكن تحسينه";
}
if ($successRate >= 95) {
    $recommendations[] = "ممتاز! الصفحة تلبي المعايير المهنية العالية";
} elseif ($successRate >= 85) {
    $recommendations[] = "جيد جداً! بعض التحسينات الطفيفة ستجعل الصفحة مثالية";
} else {
    $recommendations[] = "يحتاج إلى تحسينات إضافية لتحقيق المعايير المهنية";
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المعايير المهنية للصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            direction: rtl; 
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            line-height: 1.6;
        }
        .test-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .status-excellent { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .status-very-good { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
        .status-good { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
        .status-needs-improvement { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #667eea;
        }
        .test-pass { 
            color: #28a745; 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            margin: 0.5rem 0;
            border-left: 4px solid #28a745;
        }
        .test-fail { 
            color: #dc3545; 
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            margin: 0.5rem 0;
            border-left: 4px solid #dc3545;
        }
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        .progress-bar-custom {
            height: 15px;
            border-radius: 10px;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            transition: width 0.5s ease;
        }
        .recommendation-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 1rem;
        }
        .metric-number {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header status-<?php echo $overallStatus; ?>">
            <h1><i class="bi bi-shield-check"></i> اختبار المعايير المهنية للصفحة الرئيسية</h1>
            <p class="lead">فحص شامل لجميع جوانب الصفحة الرئيسية المحسنة للتجارة الإلكترونية العربية</p>
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-number"><?php echo $passedTests; ?></div>
                        <div>اختبار ناجح</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-number"><?php echo $totalTests - $passedTests; ?></div>
                        <div>اختبار فاشل</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-number"><?php echo $successRate; ?>%</div>
                        <div>نسبة النجاح</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-number"><?php echo count($recommendations); ?></div>
                        <div>توصية</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="test-section">
                    <h3><i class="bi bi-code-slash"></i> الاختبارات التقنية وأخطاء PHP</h3>
                    <?php
                    $technicalTests = array_slice($testResults, 5, 4);
                    foreach ($technicalTests as $test) {
                        $class = $test['result'] === 'PASS' ? 'test-pass' : 'test-fail';
                        $icon = $test['result'] === 'PASS' ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
                        echo "<div class='{$class}'><i class='bi {$icon}'></i> <strong>{$test['name']}</strong>: {$test['description']}</div>";
                    }
                    ?>
                </div>

                <div class="test-section">
                    <h3><i class="bi bi-palette-fill"></i> التصميم والمعايير البصرية</h3>
                    <?php
                    $designTests = array_slice($testResults, 9, 4);
                    foreach ($designTests as $test) {
                        $class = $test['result'] === 'PASS' ? 'test-pass' : 'test-fail';
                        $icon = $test['result'] === 'PASS' ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
                        echo "<div class='{$class}'><i class='bi {$icon}'></i> <strong>{$test['name']}</strong>: {$test['description']}</div>";
                    }
                    ?>
                </div>

                <div class="test-section">
                    <h3><i class="bi bi-translate"></i> دعم اللغة العربية و RTL</h3>
                    <?php
                    $rtlTests = array_slice($testResults, 13, 3);
                    foreach ($rtlTests as $test) {
                        $class = $test['result'] === 'PASS' ? 'test-pass' : 'test-fail';
                        $icon = $test['result'] === 'PASS' ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
                        echo "<div class='{$class}'><i class='bi {$icon}'></i> <strong>{$test['name']}</strong>: {$test['description']}</div>";
                    }
                    ?>
                </div>

                <div class="test-section">
                    <h3><i class="bi bi-speedometer2"></i> الأداء وإمكانية الوصول</h3>
                    <?php
                    $performanceTests = array_slice($testResults, 16, 6);
                    foreach ($performanceTests as $test) {
                        $class = $test['result'] === 'PASS' ? 'test-pass' : 'test-fail';
                        $icon = $test['result'] === 'PASS' ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
                        echo "<div class='{$class}'><i class='bi {$icon}'></i> <strong>{$test['name']}</strong>: {$test['description']}</div>";
                    }
                    ?>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="summary-card">
                    <h3><i class="bi bi-graph-up"></i> ملخص شامل</h3>
                    <div class="mt-3">
                        <h2><?php echo $passedTests; ?>/<?php echo $totalTests; ?></h2>
                        <p>اختبار مكتمل</p>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-custom" style="width: <?php echo $successRate; ?>%"></div>
                        </div>
                        <p class="small">نسبة النجاح: <?php echo $successRate; ?>%</p>
                        <div class="mt-3">
                            <span class="badge bg-light text-dark">الحالة: 
                                <?php 
                                $statusText = [
                                    'excellent' => 'ممتاز',
                                    'very-good' => 'جيد جداً',
                                    'good' => 'جيد',
                                    'needs-improvement' => 'يحتاج تحسين'
                                ];
                                echo $statusText[$overallStatus];
                                ?>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="test-section">
                    <h5><i class="bi bi-lightbulb"></i> التوصيات</h5>
                    <?php foreach ($recommendations as $rec): ?>
                        <div class="recommendation-card">
                            <i class="bi bi-arrow-right-circle text-primary"></i> <?php echo $rec; ?>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="test-section">
                    <h5><i class="bi bi-eye"></i> إجراءات سريعة</h5>
                    <a href="index.php" class="btn btn-primary w-100 mb-2">
                        <i class="bi bi-house-fill"></i> عرض الصفحة الرئيسية
                    </a>
                    <a href="test_homepage_enhancements.php" class="btn btn-secondary w-100 mb-2">
                        <i class="bi bi-gear-fill"></i> اختبار التحسينات
                    </a>
                    <a href="admin/homepage_settings.php" class="btn btn-success w-100">
                        <i class="bi bi-sliders"></i> إعدادات الصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Animate progress bar
        document.addEventListener('DOMContentLoaded', function() {
            const progressBar = document.querySelector('.progress-bar-custom');
            if (progressBar) {
                setTimeout(() => {
                    progressBar.style.width = '<?php echo $successRate; ?>%';
                }, 500);
            }
        });
    </script>
</body>
</html>
