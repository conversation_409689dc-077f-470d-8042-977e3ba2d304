<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="40" cy="40" r="40" fill="#667eea"/>
  
  <!-- Gradient Overlay -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="40" cy="40" r="40" fill="url(#grad1)"/>
  
  <!-- Female Avatar -->
  <!-- Face -->
  <circle cx="40" cy="32" r="12" fill="rgba(255,255,255,0.9)"/>
  
  <!-- Hair -->
  <path d="M28 28 Q40 20 52 28 Q52 35 40 35 Q28 35 28 28" fill="rgba(255,255,255,0.7)"/>
  
  <!-- Eyes -->
  <circle cx="36" cy="30" r="1.5" fill="#333"/>
  <circle cx="44" cy="30" r="1.5" fill="#333"/>
  
  <!-- Nose -->
  <circle cx="40" cy="33" r="0.5" fill="rgba(0,0,0,0.2)"/>
  
  <!-- Mouth -->
  <path d="M37 36 Q40 38 43 36" stroke="rgba(0,0,0,0.3)" stroke-width="1" fill="none" stroke-linecap="round"/>
  
  <!-- Body -->
  <path d="M30 45 Q40 42 50 45 L50 65 Q40 68 30 65 Z" fill="rgba(255,255,255,0.8)"/>
  
  <!-- Beauty/Makeup Icon -->
  <circle cx="50" cy="25" r="6" fill="rgba(255,255,255,0.3)"/>
  <path d="M47 25 L53 25 M50 22 L50 28" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
</svg>
