<?php
/**
 * Professional Featured Products Section Component
 * Modern Arabic E-commerce Featured Products Display
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description 6 columns desktop, Swiper slider mobile, professional product cards
 */

// Fetch featured products from database
$featuredProducts = [];
try {
    $featuredQuery = "
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active' AND p.is_featured = 1
        ORDER BY p.created_at DESC
        LIMIT 12
    ";
    $featuredProducts = fetchAll($featuredQuery);
    
    if (!$featuredProducts) {
        $featuredProducts = [];
    }
} catch (Exception $e) {
    error_log("Error fetching featured products: " . $e->getMessage());
    $featuredProducts = [];
}

// Function to format Iraqi Dinar price
function formatIraqiPrice($price) {
    return number_format($price, 0, '.', ',');
}

// Function to calculate discounted price
function getDiscountedPrice($price, $discount) {
    if ($discount > 0) {
        return $price - ($price * $discount / 100);
    }
    return $price;
}
?>

<!-- Featured Products Section - Professional Design -->
<section class="section featured-products-section" id="featured-products">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header">
            <h2 class="section-title">المنتجات المميزة</h2>
            <p class="section-subtitle">اكتشف أفضل منتجاتنا المختارة بعناية لتلبية احتياجاتك</p>
        </div>

        <?php if (!empty($featuredProducts)): ?>
        <!-- Desktop Grid View -->
        <div class="featured-products-desktop d-none d-lg-block">
            <div class="products-grid grid grid-cols-6">
                <?php foreach (array_slice($featuredProducts, 0, 6) as $index => $product): ?>
                <div class="product-card fade-in-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                    <!-- Product Badge -->
                    <?php if ($product['is_featured']): ?>
                    <div class="product-badge featured">الأكثر مبيعاً</div>
                    <?php endif; ?>
                    
                    <?php if ($product['discount'] > 0): ?>
                    <div class="product-badge discount">تخفيض <?php echo $product['discount']; ?>%</div>
                    <?php endif; ?>

                    <!-- Product Image -->
                    <div class="product-image-container">
                        <img src="<?php echo htmlspecialchars(getProductImage($product)); ?>"
                             class="product-image"
                             alt="<?php echo htmlspecialchars($product['name']); ?>"
                             loading="<?php echo $index < 3 ? 'eager' : 'lazy'; ?>">
                        
                        <!-- Quick Actions Overlay -->
                        <div class="product-overlay">
                            <button class="btn-quick-action btn-quick-view" 
                                    data-product-id="<?php echo $product['id']; ?>"
                                    title="مشاهدة سريعة">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn-quick-action btn-add-wishlist" 
                                    data-product-id="<?php echo $product['id']; ?>"
                                    title="إضافة للمفضلة">
                                <i class="bi bi-heart"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Product Content -->
                    <div class="product-content">
                        <h3 class="product-title">
                            <a href="product.php?id=<?php echo $product['id']; ?>">
                                <?php echo htmlspecialchars($product['name']); ?>
                            </a>
                        </h3>

                        <?php if (!empty($product['short_description'])): ?>
                        <p class="product-description">
                            <?php echo htmlspecialchars($product['short_description']); ?>
                        </p>
                        <?php endif; ?>

                        <!-- Product Price -->
                        <div class="product-price">
                            <?php if ($product['discount'] > 0): ?>
                                <span class="price-current">
                                    <?php echo formatIraqiPrice(getDiscountedPrice($product['price'], $product['discount'])); ?>
                                    <span class="currency">د.ع</span>
                                </span>
                                <span class="price-original">
                                    <?php echo formatIraqiPrice($product['price']); ?>
                                    <span class="currency">د.ع</span>
                                </span>
                            <?php else: ?>
                                <span class="price-current">
                                    <?php echo formatIraqiPrice($product['price']); ?>
                                    <span class="currency">د.ع</span>
                                </span>
                            <?php endif; ?>
                        </div>

                        <!-- Product Actions -->
                        <div class="product-actions">
                            <button class="btn-add-cart" 
                                    data-product-id="<?php echo $product['id']; ?>"
                                    data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                                    data-product-price="<?php echo getDiscountedPrice($product['price'], $product['discount']); ?>">
                                <i class="bi bi-bag-plus"></i>
                                <span>إضافة للسلة</span>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Mobile Swiper View -->
        <div class="featured-products-mobile d-lg-none">
            <div class="swiper featured-products-swiper">
                <div class="swiper-wrapper">
                    <?php foreach ($featuredProducts as $index => $product): ?>
                    <div class="swiper-slide">
                        <div class="product-card">
                            <!-- Product Badge -->
                            <?php if ($product['is_featured']): ?>
                            <div class="product-badge featured">الأكثر مبيعاً</div>
                            <?php endif; ?>
                            
                            <?php if ($product['discount'] > 0): ?>
                            <div class="product-badge discount">تخفيض <?php echo $product['discount']; ?>%</div>
                            <?php endif; ?>

                            <!-- Product Image -->
                            <div class="product-image-container">
                                <img src="<?php echo htmlspecialchars(getProductImage($product)); ?>"
                                     class="product-image"
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     loading="lazy">
                                
                                <!-- Quick Actions Overlay -->
                                <div class="product-overlay">
                                    <button class="btn-quick-action btn-quick-view" 
                                            data-product-id="<?php echo $product['id']; ?>"
                                            title="مشاهدة سريعة">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button class="btn-quick-action btn-add-wishlist" 
                                            data-product-id="<?php echo $product['id']; ?>"
                                            title="إضافة للمفضلة">
                                        <i class="bi bi-heart"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Product Content -->
                            <div class="product-content">
                                <h3 class="product-title">
                                    <a href="product.php?id=<?php echo $product['id']; ?>">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                    </a>
                                </h3>

                                <?php if (!empty($product['short_description'])): ?>
                                <p class="product-description">
                                    <?php echo htmlspecialchars($product['short_description']); ?>
                                </p>
                                <?php endif; ?>

                                <!-- Product Price -->
                                <div class="product-price">
                                    <?php if ($product['discount'] > 0): ?>
                                        <span class="price-current">
                                            <?php echo formatIraqiPrice(getDiscountedPrice($product['price'], $product['discount'])); ?>
                                            <span class="currency">د.ع</span>
                                        </span>
                                        <span class="price-original">
                                            <?php echo formatIraqiPrice($product['price']); ?>
                                            <span class="currency">د.ع</span>
                                        </span>
                                    <?php else: ?>
                                        <span class="price-current">
                                            <?php echo formatIraqiPrice($product['price']); ?>
                                            <span class="currency">د.ع</span>
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- Product Actions -->
                                <div class="product-actions">
                                    <button class="btn-add-cart" 
                                            data-product-id="<?php echo $product['id']; ?>"
                                            data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                                            data-product-price="<?php echo getDiscountedPrice($product['price'], $product['discount']); ?>">
                                        <i class="bi bi-bag-plus"></i>
                                        <span>إضافة للسلة</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Swiper Navigation -->
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
                
                <!-- Swiper Pagination -->
                <div class="swiper-pagination"></div>
            </div>
        </div>

        <!-- View All Products Button -->
        <div class="section-footer text-center mt-5">
            <a href="products.php" class="btn btn-primary btn-lg">
                <i class="bi bi-grid-3x3-gap" aria-hidden="true"></i>
                <span>عرض جميع المنتجات</span>
            </a>
        </div>

        <?php else: ?>
        <!-- No Products Available -->
        <div class="no-products text-center">
            <div class="no-products-icon">
                <i class="bi bi-bag" aria-hidden="true"></i>
            </div>
            <h3 class="no-products-title">لا توجد منتجات مميزة حالياً</h3>
            <p class="no-products-text">سيتم إضافة المنتجات المميزة قريباً. تابعونا للحصول على آخر التحديثات.</p>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Swiper CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">

<!-- Featured Products Section Styles -->
<style>
/* Featured Products Section Specific Styles */
.featured-products-section {
    background-color: var(--bg-secondary);
    padding: var(--spacing-4xl) 0;
}

.products-grid {
    gap: var(--spacing-lg);
}

/* Product Card Styles - Enhanced */
.product-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-8px);
}

.product-image-container {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
    background-color: var(--bg-light);
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

/* Product Overlay */
.product-overlay {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    opacity: 0;
    transform: translateX(-20px);
    transition: all var(--transition-normal);
}

.product-card:hover .product-overlay {
    opacity: 1;
    transform: translateX(0);
}

.btn-quick-action {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    color: var(--text-muted);
    backdrop-filter: blur(10px);
}

.btn-quick-action:hover {
    background-color: var(--primary-color);
    color: var(--text-light);
    transform: scale(1.1);
}

/* Product Badges */
.product-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background-color: var(--accent-color);
    color: var(--text-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    z-index: 3;
}

.product-badge.featured {
    background-color: var(--warning-color);
}

.product-badge.discount {
    background-color: var(--success-color);
}

.product-badge.new {
    background-color: var(--primary-color);
}

/* Product Content */
.product-content {
    padding: var(--spacing-lg);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-title {
    margin-bottom: var(--spacing-sm);
    flex-shrink: 0;
}

.product-title a {
    font-size: var(--text-lg);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: var(--font-semibold);
    line-height: var(--leading-snug);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: color var(--transition-normal);
}

.product-title a:hover {
    color: var(--primary-color);
}

.product-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-normal);
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
}

/* Product Price */
.product-price {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    flex-shrink: 0;
}

.price-current {
    font-size: var(--text-xl);
    color: var(--text-primary);
    font-weight: var(--font-bold);
}

.price-original {
    font-size: var(--text-base);
    color: var(--text-muted);
    text-decoration: line-through;
}

.currency {
    font-size: var(--currency-font-size);
    color: var(--text-muted);
    margin-right: var(--spacing-xs);
}

/* Product Actions */
.product-actions {
    flex-shrink: 0;
}

.btn-add-cart {
    width: 100%;
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.btn-add-cart:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Swiper Styles */
.featured-products-swiper {
    padding: var(--spacing-lg) 0 var(--spacing-2xl);
}

.featured-products-swiper .swiper-slide {
    height: auto;
}

.featured-products-swiper .swiper-button-next,
.featured-products-swiper .swiper-button-prev {
    color: var(--primary-color);
    background-color: rgba(255, 255, 255, 0.9);
    width: 44px;
    height: 44px;
    border-radius: 50%;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.featured-products-swiper .swiper-button-next:hover,
.featured-products-swiper .swiper-button-prev:hover {
    background-color: var(--primary-color);
    color: var(--text-light);
    transform: scale(1.1);
}

.featured-products-swiper .swiper-button-next::after,
.featured-products-swiper .swiper-button-prev::after {
    font-size: 18px;
}

.featured-products-swiper .swiper-pagination-bullet {
    background-color: var(--text-muted);
    opacity: 0.5;
}

.featured-products-swiper .swiper-pagination-bullet-active {
    background-color: var(--primary-color);
    opacity: 1;
}

/* No Products State */
.no-products {
    padding: var(--spacing-4xl) 0;
}

.no-products-icon {
    font-size: var(--text-6xl);
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

.no-products-title {
    font-size: var(--text-2xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.no-products-text {
    font-size: var(--text-base);
    color: var(--text-secondary);
    max-width: 500px;
    margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .products-grid.grid-cols-6 {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 992px) {
    .products-grid.grid-cols-6 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .featured-products-section {
        padding: var(--spacing-3xl) 0;
    }

    .product-image-container {
        height: 200px;
    }

    .product-content {
        padding: var(--spacing-md);
    }
}
</style>

<!-- Swiper JS -->
<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>

<!-- Featured Products JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Swiper for mobile
    const featuredSwiper = new Swiper('.featured-products-swiper', {
        slidesPerView: 1,
        spaceBetween: 20,
        loop: true,
        autoplay: {
            delay: 4000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        breakpoints: {
            480: {
                slidesPerView: 2,
                spaceBetween: 20,
            },
            768: {
                slidesPerView: 2,
                spaceBetween: 24,
            }
        }
    });

    // Add to cart functionality
    document.querySelectorAll('.btn-add-cart').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            const productName = this.dataset.productName;
            const productPrice = this.dataset.productPrice;

            // Add loading state
            const originalContent = this.innerHTML;
            this.innerHTML = '<i class="bi bi-arrow-repeat"></i> <span>جاري الإضافة...</span>';
            this.disabled = true;

            // Simulate add to cart (replace with actual AJAX call)
            setTimeout(() => {
                this.innerHTML = '<i class="bi bi-check"></i> <span>تمت الإضافة</span>';
                this.style.backgroundColor = 'var(--success-color)';

                // Show success message
                showToast('تم إضافة المنتج إلى السلة بنجاح', 'success');

                // Reset button after 2 seconds
                setTimeout(() => {
                    this.innerHTML = originalContent;
                    this.style.backgroundColor = '';
                    this.disabled = false;
                }, 2000);
            }, 1000);
        });
    });

    // Quick view functionality
    document.querySelectorAll('.btn-quick-view').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            // Implement quick view modal
            console.log('Quick view for product:', productId);
        });
    });

    // Wishlist functionality
    document.querySelectorAll('.btn-add-wishlist').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            this.classList.toggle('active');

            if (this.classList.contains('active')) {
                this.innerHTML = '<i class="bi bi-heart-fill"></i>';
                this.style.color = 'var(--accent-color)';
                showToast('تم إضافة المنتج إلى المفضلة', 'success');
            } else {
                this.innerHTML = '<i class="bi bi-heart"></i>';
                this.style.color = '';
                showToast('تم إزالة المنتج من المفضلة', 'info');
            }
        });
    });
});

// Toast notification function
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="bi bi-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add to page
    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => toast.classList.add('show'), 100);

    // Remove toast
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
}
</script>
