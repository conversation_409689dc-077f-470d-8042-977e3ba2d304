<?php
/**
 * Professional Hero Carousel Component - Rebuilt from Scratch
 * Modern Arabic E-commerce Design with 5 Slides
 *
 * @version 3.0
 * <AUTHOR> Development Team
 * @description 5 slides carousel with 5-second auto-advance, rectangular design, clear CTA buttons
 */

// Default carousel slides data - Professional Arabic E-commerce
$carouselSlides = [
    [
        'id' => 1,
        'image' => 'assets/images/carousel/slide-1.jpg',
        'title' => 'اكتشفي سر الجمال الطبيعي مع Care',
        'subtitle' => 'منتجات عناية متكاملة لشعرك وبشرتك بأفضل المكونات الطبيعية',
        'cta_primary' => 'تسوق الآن',
        'cta_primary_url' => '#featured-products',
        'cta_secondary' => 'العروض',
        'cta_secondary_url' => '#special-offers',
        'active' => true
    ],
    [
        'id' => 2,
        'image' => 'assets/images/carousel/slide-2.jpg',
        'title' => 'عروض حصرية لفترة محدودة',
        'subtitle' => 'خصومات تصل إلى 50% على مجموعة مختارة من منتجات العناية',
        'cta_primary' => 'تسوق الآن',
        'cta_primary_url' => '#featured-products',
        'cta_secondary' => 'العروض',
        'cta_secondary_url' => '#special-offers',
        'active' => false
    ],
    [
        'id' => 3,
        'image' => 'assets/images/carousel/slide-3.jpg',
        'title' => 'منتجات طبيعية 100%',
        'subtitle' => 'مكونات طبيعية آمنة ومجربة لجميع أنواع البشرة والشعر',
        'cta_primary' => 'تسوق الآن',
        'cta_primary_url' => '#featured-products',
        'cta_secondary' => 'العروض',
        'cta_secondary_url' => '#special-offers',
        'active' => false
    ],
    [
        'id' => 4,
        'image' => 'assets/images/carousel/slide-4.jpg',
        'title' => 'توصيل مجاني لجميع أنحاء العراق',
        'subtitle' => 'اطلبي الآن واستمتعي بالتوصيل المجاني السريع والآمن',
        'cta_primary' => 'تسوق الآن',
        'cta_primary_url' => '#featured-products',
        'cta_secondary' => 'العروض',
        'cta_secondary_url' => '#special-offers',
        'active' => false
    ],
    [
        'id' => 5,
        'image' => 'assets/images/carousel/slide-5.jpg',
        'title' => 'ضمان الجودة والأصالة',
        'subtitle' => 'جميع منتجاتنا أصلية ومضمونة مع إمكانية الإرجاع خلال 14 يوم',
        'cta_primary' => 'تسوق الآن',
        'cta_primary_url' => '#featured-products',
        'cta_secondary' => 'العروض',
        'cta_secondary_url' => '#special-offers',
        'active' => false
    ]
];

// Carousel settings
$autoAdvanceTime = 5000; // 5 seconds
$showIndicators = true;
$showControls = true;
$totalSlides = count($carouselSlides);
?>

<!-- Professional Hero Carousel Section - Rectangular Design -->
<section class="hero-section" id="heroSection" role="banner" aria-label="عرض الشرائح الرئيسية">
    <!-- Carousel Container -->
    <div class="hero-carousel"
         id="heroCarousel"
         data-auto-advance="<?php echo $autoAdvanceTime; ?>"
         data-slides-count="<?php echo $totalSlides; ?>"
         role="region"
         aria-label="كاروسيل الصور الرئيسية">

        <!-- Carousel Slides -->
        <div class="carousel-slides" id="carouselSlides">
            <?php foreach ($carouselSlides as $index => $slide): ?>
                <div class="carousel-slide <?php echo $slide['active'] ? 'active' : ''; ?>"
                     data-slide="<?php echo $index; ?>"
                     role="group"
                     aria-roledescription="شريحة"
                     aria-label="شريحة <?php echo $index + 1; ?> من <?php echo $totalSlides; ?>">

                    <!-- Image Container -->
                    <div class="slide-image-container">
                        <!-- Loading Placeholder -->
                        <div class="slide-loading" aria-hidden="true">
                            <div class="loading-spinner"></div>
                            <span class="loading-text">جاري التحميل...</span>
                        </div>

                        <!-- Main Image -->
                        <img src="<?php echo htmlspecialchars($slide['image']); ?>"
                             class="slide-image"
                             alt="<?php echo htmlspecialchars($slide['title']); ?>"
                             loading="<?php echo $slide['active'] ? 'eager' : 'lazy'; ?>"
                             onload="this.parentElement.querySelector('.slide-loading').style.display='none'"
                             onerror="this.src='assets/images/placeholder-carousel.jpg'">
                    </div>

                    <!-- Content Overlay -->
                    <div class="slide-content" role="group" aria-label="محتوى الشريحة">
                        <div class="container">
                            <div class="content-wrapper">
                                <!-- Slide Title -->
                                <h1 class="slide-title" data-animation="slideUp">
                                    <?php echo htmlspecialchars($slide['title']); ?>
                                </h1>

                                <!-- Slide Subtitle -->
                                <p class="slide-subtitle" data-animation="slideUp" data-delay="200">
                                    <?php echo htmlspecialchars($slide['subtitle']); ?>
                                </p>

                                <!-- CTA Buttons - Only on first slide -->
                                <?php if ($slide['active']): ?>
                                <div class="slide-actions" data-animation="fadeIn" data-delay="400">
                                    <a href="<?php echo htmlspecialchars($slide['cta_primary_url']); ?>"
                                       class="btn btn-primary btn-hero"
                                       role="button">
                                        <i class="bi bi-bag-fill" aria-hidden="true"></i>
                                        <span><?php echo htmlspecialchars($slide['cta_primary']); ?></span>
                                    </a>
                                    <a href="<?php echo htmlspecialchars($slide['cta_secondary_url']); ?>"
                                       class="btn btn-secondary btn-hero"
                                       role="button">
                                        <i class="bi bi-percent" aria-hidden="true"></i>
                                        <span><?php echo htmlspecialchars($slide['cta_secondary']); ?></span>
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Navigation Controls -->
        <?php if ($showControls): ?>
        <div class="carousel-controls" role="group" aria-label="أزرار التنقل">
            <button class="carousel-btn carousel-btn-prev"
                    type="button"
                    id="carouselPrev"
                    aria-label="الشريحة السابقة">
                <i class="bi bi-chevron-right" aria-hidden="true"></i>
            </button>
            <button class="carousel-btn carousel-btn-next"
                    type="button"
                    id="carouselNext"
                    aria-label="الشريحة التالية">
                <i class="bi bi-chevron-left" aria-hidden="true"></i>
            </button>
        </div>
        <?php endif; ?>

        <!-- Indicators -->
        <?php if ($showIndicators): ?>
        <div class="carousel-indicators" role="tablist" aria-label="مؤشرات الشرائح">
            <?php foreach ($carouselSlides as $index => $slide): ?>
                <button class="indicator <?php echo $slide['active'] ? 'active' : ''; ?>"
                        type="button"
                        role="tab"
                        data-slide="<?php echo $index; ?>"
                        aria-label="الذهاب إلى الشريحة <?php echo $index + 1; ?>"
                        aria-selected="<?php echo $slide['active'] ? 'true' : 'false'; ?>">
                </button>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Carousel JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const carousel = document.getElementById('heroCarousel');
    const slides = carousel.querySelectorAll('.carousel-slide');
    const indicators = carousel.querySelectorAll('.indicator');
    const prevBtn = document.getElementById('carouselPrev');
    const nextBtn = document.getElementById('carouselNext');

    let currentSlide = 0;
    const totalSlides = <?php echo $totalSlides; ?>;
    const autoAdvanceTime = <?php echo $autoAdvanceTime; ?>;
    let autoAdvanceInterval;

    // Function to show specific slide
    function showSlide(index) {
        // Hide all slides
        slides.forEach(slide => slide.classList.remove('active'));
        indicators.forEach(indicator => {
            indicator.classList.remove('active');
            indicator.setAttribute('aria-selected', 'false');
        });

        // Show current slide
        slides[index].classList.add('active');
        indicators[index].classList.add('active');
        indicators[index].setAttribute('aria-selected', 'true');

        currentSlide = index;
    }

    // Function to go to next slide
    function nextSlide() {
        const next = (currentSlide + 1) % totalSlides;
        showSlide(next);
    }

    // Function to go to previous slide
    function prevSlide() {
        const prev = (currentSlide - 1 + totalSlides) % totalSlides;
        showSlide(prev);
    }

    // Auto advance functionality
    function startAutoAdvance() {
        autoAdvanceInterval = setInterval(nextSlide, autoAdvanceTime);
    }

    function stopAutoAdvance() {
        clearInterval(autoAdvanceInterval);
    }

    // Event listeners
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            nextSlide();
            stopAutoAdvance();
            setTimeout(startAutoAdvance, 3000); // Restart after 3 seconds
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            prevSlide();
            stopAutoAdvance();
            setTimeout(startAutoAdvance, 3000); // Restart after 3 seconds
        });
    }

    // Indicator click events
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            showSlide(index);
            stopAutoAdvance();
            setTimeout(startAutoAdvance, 3000); // Restart after 3 seconds
        });
    });

    // Pause on hover
    carousel.addEventListener('mouseenter', stopAutoAdvance);
    carousel.addEventListener('mouseleave', startAutoAdvance);

    // Start auto advance
    startAutoAdvance();

    // Keyboard navigation
    carousel.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
            nextSlide();
            stopAutoAdvance();
            setTimeout(startAutoAdvance, 3000);
        } else if (e.key === 'ArrowRight') {
            prevSlide();
            stopAutoAdvance();
            setTimeout(startAutoAdvance, 3000);
        }
    });
});
</script>




