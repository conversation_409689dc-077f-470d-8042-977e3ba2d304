<?php
/**
 * Professional Influencers Section Component
 * Modern Arabic E-commerce Influencers Display
 *
 * @version 1.0
 * <AUTHOR> Development Team
 * @description Professional influencers section with database integration
 */

// Fetch influencers from database
try {
    $influencers = fetchAll("
        SELECT influencer_name, influencer_image, content_title, content_text, 
               rating, is_featured, sort_order, published_at, created_at,
               social_instagram, social_youtube, social_tiktok, followers_count
        FROM influencers_content
        WHERE status = 'published'
        ORDER BY is_featured DESC, sort_order ASC, published_at DESC, created_at DESC
        LIMIT 12
    ") ?: [];
} catch (Exception $e) {
    error_log("Error fetching influencers: " . $e->getMessage());
    $influencers = [];
}

// Function to format follower count
function formatFollowerCount($count) {
    if ($count >= 1000000) {
        return number_format($count / 1000000, 1) . 'M';
    } elseif ($count >= 1000) {
        return number_format($count / 1000, 1) . 'K';
    }
    return number_format($count);
}

// Function to get social media icon
function getSocialIcon($platform) {
    $icons = [
        'instagram' => 'bi-instagram',
        'youtube' => 'bi-youtube',
        'tiktok' => 'bi-tiktok',
        'twitter' => 'bi-twitter',
        'facebook' => 'bi-facebook'
    ];
    return $icons[$platform] ?? 'bi-link';
}
?>

<!-- Influencers Section - Professional Design -->
<section class="section influencers-section" id="influencers">
    <div class="container">
        <!-- Section Header -->
        <div class="section-header text-center">
            <span class="section-label">سفراء العلامة التجارية</span>
            <h2 class="section-title">المؤثرون الذين يثقون بنا</h2>
            <p class="section-subtitle">تعرف على نخبة من المؤثرين والخبراء الذين اختاروا منتجاتنا وشاركوا تجاربهم الإيجابية</p>
        </div>

        <?php if (!empty($influencers)): ?>
        <!-- Influencers Grid -->
        <div class="influencers-grid">
            <?php foreach (array_slice($influencers, 0, 6) as $index => $influencer): ?>
            <div class="influencer-card fade-in-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                <!-- Influencer Image -->
                <div class="influencer-image-container">
                    <?php if (!empty($influencer['influencer_image'])): ?>
                    <img src="<?php echo htmlspecialchars($influencer['influencer_image']); ?>"
                         alt="<?php echo htmlspecialchars($influencer['influencer_name']); ?>"
                         class="influencer-image"
                         loading="lazy">
                    <?php else: ?>
                    <div class="influencer-placeholder">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Featured Badge -->
                    <?php if ($influencer['is_featured']): ?>
                    <div class="featured-badge">
                        <i class="bi bi-star-fill"></i>
                        <span>مميز</span>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Social Media Links -->
                    <div class="social-overlay">
                        <?php if (!empty($influencer['social_instagram'])): ?>
                        <a href="<?php echo htmlspecialchars($influencer['social_instagram']); ?>" 
                           class="social-link instagram" target="_blank" rel="noopener">
                            <i class="bi bi-instagram"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($influencer['social_youtube'])): ?>
                        <a href="<?php echo htmlspecialchars($influencer['social_youtube']); ?>" 
                           class="social-link youtube" target="_blank" rel="noopener">
                            <i class="bi bi-youtube"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($influencer['social_tiktok'])): ?>
                        <a href="<?php echo htmlspecialchars($influencer['social_tiktok']); ?>" 
                           class="social-link tiktok" target="_blank" rel="noopener">
                            <i class="bi bi-tiktok"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Influencer Content -->
                <div class="influencer-content">
                    <!-- Name and Title -->
                    <div class="influencer-header">
                        <h3 class="influencer-name"><?php echo htmlspecialchars($influencer['influencer_name']); ?></h3>
                        
                        <?php if (!empty($influencer['content_title'])): ?>
                        <div class="influencer-title"><?php echo htmlspecialchars($influencer['content_title']); ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Followers Count -->
                    <?php if (!empty($influencer['followers_count'])): ?>
                    <div class="followers-count">
                        <i class="bi bi-people-fill"></i>
                        <span><?php echo formatFollowerCount($influencer['followers_count']); ?> متابع</span>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Content Text -->
                    <p class="influencer-description"><?php echo htmlspecialchars($influencer['content_text']); ?></p>
                    
                    <!-- Rating -->
                    <?php if (!empty($influencer['rating'])): ?>
                    <div class="influencer-rating">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="star <?php echo $i <= $influencer['rating'] ? 'filled' : 'empty'; ?>">★</i>
                        <?php endfor; ?>
                        <span class="rating-text"><?php echo $influencer['rating']; ?>/5</span>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Card Footer -->
                <div class="influencer-footer">
                    <button class="btn-follow" data-influencer="<?php echo htmlspecialchars($influencer['influencer_name']); ?>">
                        <i class="bi bi-person-plus"></i>
                        <span>متابعة</span>
                    </button>
                    
                    <button class="btn-share" data-influencer="<?php echo htmlspecialchars($influencer['influencer_name']); ?>">
                        <i class="bi bi-share"></i>
                        <span>مشاركة</span>
                    </button>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Show More Button -->
        <?php if (count($influencers) > 6): ?>
        <div class="influencers-actions text-center">
            <button class="btn btn-outline btn-lg" id="loadMoreInfluencers">
                <i class="bi bi-arrow-down-circle"></i>
                <span>عرض المزيد من المؤثرين</span>
            </button>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <!-- No Influencers State -->
        <div class="no-influencers text-center">
            <div class="no-influencers-icon">
                <i class="bi bi-people" aria-hidden="true"></i>
            </div>
            <h3 class="no-influencers-title">لا يوجد مؤثرون حالياً</h3>
            <p class="no-influencers-text">نعمل على إضافة المزيد من المؤثرين والخبراء قريباً</p>
        </div>
        <?php endif; ?>

        <!-- Become Influencer CTA -->
        <div class="become-influencer-cta">
            <div class="cta-content">
                <div class="cta-text">
                    <h3 class="cta-title">هل أنت مؤثر أو خبير في مجال الجمال؟</h3>
                    <p class="cta-description">انضم إلى فريق سفراء العلامة التجارية واحصل على منتجات مجانية وعمولات حصرية</p>
                </div>
                <div class="cta-actions">
                    <a href="become-influencer.php" class="btn btn-primary btn-lg">
                        <i class="bi bi-star"></i>
                        <span>انضم كسفير</span>
                    </a>
                    <a href="contact.php" class="btn btn-outline btn-lg">
                        <i class="bi bi-envelope"></i>
                        <span>تواصل معنا</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Influencers Section Styles -->
<style>
/* Influencers Section Styles */
.influencers-section {
    background-color: var(--bg-primary);
    padding: var(--spacing-5xl) 0;
    position: relative;
}

.influencers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-4xl);
}

/* Influencer Card Styles */
.influencer-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-normal);
    position: relative;
}

.influencer-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

/* Influencer Image */
.influencer-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.influencer-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.influencer-card:hover .influencer-image {
    transform: scale(1.05);
}

.influencer-placeholder {
    width: 100%;
    height: 100%;
    background: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-6xl);
    color: var(--text-muted);
}

/* Featured Badge */
.featured-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: var(--warning-color);
    color: var(--text-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    z-index: 2;
}

/* Social Overlay */
.social-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.influencer-card:hover .social-overlay {
    opacity: 1;
}

.social-link {
    width: 45px;
    height: 45px;
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--text-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    text-decoration: none;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.social-link:hover {
    transform: scale(1.1);
    background-color: var(--text-light);
}

.social-link.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
    color: var(--text-light);
}

.social-link.youtube:hover {
    background-color: #ff0000;
    color: var(--text-light);
}

.social-link.tiktok:hover {
    background-color: #000000;
    color: var(--text-light);
}

/* Influencer Content */
.influencer-content {
    padding: var(--spacing-xl);
}

.influencer-header {
    margin-bottom: var(--spacing-md);
}

.influencer-name {
    font-size: var(--text-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-semibold);
}

.influencer-title {
    font-size: var(--text-sm);
    color: var(--primary-color);
    font-weight: var(--font-medium);
}

/* Followers Count */
.followers-count {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-muted);
    font-size: var(--text-sm);
    margin-bottom: var(--spacing-md);
}

.followers-count i {
    color: var(--primary-color);
}

/* Description */
.influencer-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Rating */
.influencer-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
}

.star {
    color: var(--warning-color);
    font-size: var(--text-sm);
}

.star.empty {
    color: var(--text-muted);
    opacity: 0.3;
}

.rating-text {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    margin-right: var(--spacing-xs);
}

/* Influencer Footer */
.influencer-footer {
    display: flex;
    gap: var(--spacing-sm);
    padding: 0 var(--spacing-xl) var(--spacing-xl);
}

.btn-follow,
.btn-share {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--primary-color);
    background: none;
    color: var(--primary-color);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.btn-follow:hover {
    background-color: var(--primary-color);
    color: var(--text-light);
}

.btn-share {
    border-color: var(--text-muted);
    color: var(--text-muted);
}

.btn-share:hover {
    background-color: var(--text-muted);
    color: var(--text-light);
}

/* No Influencers State */
.no-influencers {
    padding: var(--spacing-5xl) 0;
}

.no-influencers-icon {
    font-size: var(--text-6xl);
    color: var(--text-muted);
    margin-bottom: var(--spacing-xl);
    opacity: 0.5;
}

.no-influencers-title {
    font-size: var(--text-2xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.no-influencers-text {
    font-size: var(--text-base);
    color: var(--text-secondary);
    max-width: 500px;
    margin: 0 auto;
}

/* Become Influencer CTA */
.become-influencer-cta {
    background: var(--primary-gradient);
    color: var(--text-light);
    padding: var(--spacing-4xl);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-4xl);
}

.cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-2xl);
    align-items: center;
}

.cta-title {
    font-size: var(--text-2xl);
    margin-bottom: var(--spacing-md);
}

.cta-description {
    font-size: var(--text-base);
    opacity: 0.9;
    line-height: var(--leading-relaxed);
}

.cta-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-shrink: 0;
}

.become-influencer-cta .btn {
    background-color: var(--text-light);
    color: var(--primary-color);
    border-color: var(--text-light);
}

.become-influencer-cta .btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.become-influencer-cta .btn-outline {
    background-color: transparent;
    color: var(--text-light);
    border-color: var(--text-light);
}

.become-influencer-cta .btn-outline:hover {
    background-color: var(--text-light);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .influencers-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cta-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .cta-actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .influencers-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .influencer-image-container {
        height: 200px;
    }
    
    .cta-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .social-overlay {
        opacity: 1;
        background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, transparent 50%);
        align-items: flex-end;
        padding-bottom: var(--spacing-md);
    }
    
    .social-link {
        width: 35px;
        height: 35px;
        font-size: var(--text-base);
    }
}
</style>

<!-- Influencers JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load more influencers functionality
    const loadMoreBtn = document.getElementById('loadMoreInfluencers');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            this.innerHTML = '<i class="bi bi-arrow-repeat"></i> <span>جاري التحميل...</span>';
            this.disabled = true;
            
            setTimeout(() => {
                this.innerHTML = '<i class="bi bi-check"></i> <span>تم تحميل المزيد</span>';
                setTimeout(() => {
                    this.style.display = 'none';
                }, 1000);
            }, 1500);
        });
    }
    
    // Follow button functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-follow')) {
            const btn = e.target.closest('.btn-follow');
            const influencerName = btn.dataset.influencer;
            
            btn.innerHTML = '<i class="bi bi-check"></i> <span>تمت المتابعة</span>';
            btn.style.backgroundColor = 'var(--success-color)';
            btn.style.borderColor = 'var(--success-color)';
            btn.style.color = 'var(--text-light)';
            btn.disabled = true;
            
            if (window.toastManager) {
                window.toastManager.show(`تمت متابعة ${influencerName} بنجاح`, 'success');
            }
        }
    });
    
    // Share button functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-share')) {
            const btn = e.target.closest('.btn-share');
            const influencerName = btn.dataset.influencer;
            
            if (navigator.share) {
                navigator.share({
                    title: `${influencerName} - سفير متجر Care`,
                    text: `تعرف على ${influencerName} أحد سفراء متجر Care للعناية والجمال`,
                    url: window.location.href
                });
            } else {
                if (window.toastManager) {
                    window.toastManager.show('تم نسخ الرابط إلى الحافظة', 'info');
                }
            }
        }
    });
});
</script>
